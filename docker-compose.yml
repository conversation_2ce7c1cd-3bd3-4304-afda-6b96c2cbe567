version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:4.4
    container_name: travelgator_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: travelgator_development
    ports:
      - "27018:27017"
    # volumes:
    #   - mongodb_data:/data/db
    #   - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    # networks:
    #   - travelgator_network

  # Redis for Sidekiq and caching
  redis:
    image: redis:6.2-alpine
    container_name: travelgator_redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    # volumes:
    #   - redis_data:/data
    # networks:
    #   - travelgator_network

  # Rails Application
  # web:
  #   build: .
  #   container_name: travelgator_web
  #   restart: unless-stopped
  #   environment:
  #     RAILS_ENV: development
  #     MONGODB_URI: **************************************/travelgator_development?authSource=admin
  #     REDIS_URL: redis://redis:6379/0
  #     RAILS_MASTER_KEY: ${RAILS_MASTER_KEY}
  #   ports:
  #     - "3000:3000"
  #   volumes:
  #     - .:/app
  #     - bundle_cache:/usr/local/bundle
  #     - node_modules:/app/node_modules
  #   depends_on:
  #     - mongodb
  #     - redis
  #   networks:
  #     - travelgator_network
  #   stdin_open: true
  #   tty: true

  # Sidekiq for background jobs
  # sidekiq:
  #   build: .
  #   container_name: travelgator_sidekiq
  #   restart: unless-stopped
  #   environment:
  #     RAILS_ENV: development
  #     MONGODB_URI: **************************************/travelgator_development?authSource=admin
  #     REDIS_URL: redis://redis:6379/0
  #     RAILS_MASTER_KEY: ${RAILS_MASTER_KEY}
  #   volumes:
  #     - .:/app
  #     - bundle_cache:/usr/local/bundle
  #   depends_on:
  #     - mongodb
  #     - redis
  #   networks:
  #     - travelgator_network
  #   command: bundle exec sidekiq

  # MongoDB Express (Web UI for MongoDB)
  # mongo-express:
  #   image: mongo-express:latest
  #   container_name: travelgator_mongo_express
  #   restart: unless-stopped
  #   environment:
  #     ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_ROOT_USERNAME:-admin}
  #     ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_ROOT_PASSWORD:-password}
  #     ME_CONFIG_MONGODB_URL: **************************************/
  #     ME_CONFIG_BASICAUTH_USERNAME: ${MONGO_EXPRESS_USERNAME:-admin}
  #     ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_EXPRESS_PASSWORD:-admin}
  #   ports:
  #     - "8081:8081"
  #   depends_on:
  #     - mongodb
  #   networks:
  #     - travelgator_network

# volumes:
#   mongodb_data:
#   redis_data:
#   bundle_cache:
#   node_modules:

# networks:
#   travelgator_network:
#     driver: bridge
