---
description: TravelGator Rails Rules - AI-Ready Backend Standards
globs: "**/*.rb"
alwaysApply: true
---

# Project Context

TravelGator enforces strict Rails 5.2 compatibility, exception-based error handling, and consistent API design for Ruby on Rails backend applications using Mongoid ODM and service-oriented architecture.

## Key Principles

- Zero configuration required for new developers
- Models and services throw exceptions - controllers rescue and respond
- Maximum type safety with explicit field types
- AI-friendly code generation patterns

## Before Writing Code

1. Analyze existing service patterns in the codebase
2. Consider error scenarios and exception handling flow
3. Follow the rules below strictly
4. Validate Mongoid document structure
5. Avoid code duplication across services

## Rules

### Version Compatibility

- Don't use Rails 7+ features like `Rails.error` - not available in Rails 5.2.
- Use `Rails.logger` instead of `Rails.error` for error reporting.
- Stick to Rails 5.2 compatible gems and patterns.
- Use Ruby 2.6.8 compatible syntax only.
- Use Mongoid 6.4.0 compatible methods only.

### Error Handling Architecture

- **Models/Services**: Always throw exceptions when operations fail.
- **Controllers**: Always rescue exceptions and return standardized responses to users.
- Always log errors with context (user_id, request_id, operation) before responding.
- Use structured logging with <PERSON><PERSON><PERSON> for production.
- Include appropriate HTTP status codes in all error responses.
- Use consistent error response format across all APIs.
- Use `new` when throwing an error - `raise StandardError.new("message")`.

### Model Error Handling

- Don't return false from models - throw exceptions instead.
- Use Rails validations for basic checks, throw exceptions for complex business rules.
- Always validate critical fields first for fail-fast behavior.
- Use `raise` with descriptive error messages.
- Don't use custom exception classes - use `StandardError` with clear messages.

### Service Layer Error Handling

- Always throw exceptions when operations fail.
- Use service objects for complex business logic.
- Keep services focused and single-purpose.
- Fail fast by validating critical inputs first.
- Chain operations in order of importance.
- Log errors before re-raising them.
- Use descriptive error messages that help with debugging.

### Controller Design Patterns

- Keep controllers thin - maximum 10 lines per action.
- Move business logic to services.
- Always rescue exceptions from services and return standardized error responses.
- Handle only request/response flow and parameter validation.
- Use strong parameters for all input validation.
- Return consistent JSON responses using `render_success`/`render_error`.
- Include `ErrorHandling` concern for standardized error handling.
- Use `Authenticatable` concern for JWT authentication.

### Mongoid Document Structure

- Always include `Documentable` concern for all models.
- Use explicit field types for all document fields.
- Use `embeds_many`/`embeds_one` for nested documents.
- Use `references_many`/`references_one` for relationships.
- Don't use implicit field types - always specify `type: String`, `type: Float`, etc.
- Don't use `set` method - use `update` instead.
- Always check for errors after `update` or `create` operations.

### API Response Standards

- Use consistent response format with success/error indicators.
- Include error details with user-friendly messages.
- Use appropriate HTTP status codes (200, 201, 400, 401, 404, 422, 500).
- Include meta information for collections (pagination, totals).
- Use `BaseController` render_success/render_error methods.
- Follow RESTful conventions for resource endpoints.
- Use consistent URL patterns: `/api/resource_name`.

### Authentication and Security

- Use JWT tokens with proper expiration (24 hours default).
- Validate tokens on every authenticated request.
- Use Firebase for user authentication.
- Store sensitive data in environment variables.
- Validate all inputs using strong parameters.
- Sanitize user input to prevent injection attacks.
- Never trust client-side validation alone.

### Background Jobs and Workers

- Use Sidekiq for background processing.
- Inherit from `BaseWorker` for consistent patterns.
- Implement proper error handling and retries.
- Use Redis for job queuing and caching.
- Keep jobs idempotent - safe to run multiple times.
- Use small, focused jobs rather than large monolithic ones.
- Use exponential backoff for retries.

### External Integrations

- Use service objects for external API interactions.
- Implement circuit breaker patterns for reliability.
- Cache API responses when appropriate.
- Handle network timeouts and failures gracefully.
- Use structured logging for debugging integrations.
- Use `FirebaseAuthService` for token verification.
- Use GraphQL API for all Shopify operations.

### Performance and Optimization

- Use Redis for application caching.
- Implement cache invalidation strategies.
- Cache expensive database queries and API calls.
- Add proper indexes for query optimization.
- Use aggregation pipelines for complex queries.
- Monitor slow queries and optimize them.

### Code Organization and Structure

- Use `app/models/` for Mongoid documents and business logic.
- Use `app/models/concerns/` for shared modules (Documentable, etc.).
- Use `app/services/` for business logic and external integrations.
- Use `app/controllers/api/` for API controllers.
- Use `app/controllers/concerns/` for controller modules (Authenticatable, ErrorHandling).
- Use descriptive, intention-revealing names.
- Follow Rails naming conventions.
- Avoid abbreviations and unclear acronyms.

### Testing Best Practices

- Use RSpec for all testing (models, services, controllers).
- Follow AAA pattern: Arrange, Act, Assert.
- Use descriptive contexts and examples.
- Mock external dependencies (Firebase, Shopify).
- Test both happy and unhappy paths.
- Use Faker for generating test data.
- Aim for 80%+ test coverage on critical paths.

## Example: Exception-Based Error Handling

```ruby
# ✅ Good: Service throws exceptions, controller rescues and responds
class OrderService
  def create_order(params)
    # Fail fast on critical validations
    raise StandardError.new("Missing user_id") if params[:user_id].blank?
    raise StandardError.new("Missing items") if params[:items].blank?

    # Create and validate order
    order = Order.new(params)
    raise StandardError.new(order.errors.full_messages.join(", ")) unless order.valid?

    # Process in order of importance
    process_payment(params)
    create_order_record(order)
    send_notifications
  rescue StandardError => e
    Rails.logger.error "Order creation failed: #{e.message}"
    raise e
  end

  private

  def process_payment(params)
    raise StandardError.new("Invalid payment amount") if params[:amount].to_f <= 0
    raise StandardError.new("Payment method required") if params[:payment_method].blank?
  end
end

class Api::OrdersController < Api::BaseController
  include Authenticatable
  include ErrorHandling

  def create
    with_error_handling("order creation") do
      order = OrderService.new.create_order(order_params)
      render_success(order_data(order), status: :created)
    end
  end

  private

  def order_params
    params.require(:order).permit(:items => [], :total_price, :currency)
  end
end

# ❌ Bad: Swallowing errors and returning nil
class OrderService
  def create_order(params)
    begin
      order = Order.new(params)
      order.save
    rescue => e
      logger.info "Order failed but continuing..."
      return nil
    end
  end
end
```
