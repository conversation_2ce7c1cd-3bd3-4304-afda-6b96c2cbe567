---
description:
globs:
alwaysApply: true
---
# RSpec Best Practices

specs:
  # General Rules
  rules:
    - Use descriptive contexts and examples
    - Keep tests focused and atomic
    - Follow arrange-act-assert pattern
    - Use factories instead of direct model creation
    - Test happy and unhappy paths
    - Avoid testing implementation details
    - Use shared examples for common behavior
    - Keep tests DRY but readable

  # Model Specs
  models:
    - Test validations
    - Test associations
    - Test scopes
    - Test instance methods
    - Test callbacks if critical
    example: |
      RSpec.describe User do
        describe "validations" do
          it { is_expected.to validate_presence_of(:email) }
          it { is_expected.to validate_uniqueness_of(:email) }
        end

        describe "associations" do
          it { is_expected.to have_many(:orders) }
        end

        describe "#full_name" do
          let(:user) { create(:user, first_name: "<PERSON>", last_name: "<PERSON><PERSON>") }

          it "returns the full name" do
            expect(user.full_name).to eq("John Doe")
          end
        end
      end

  # Controller Specs
  controllers:
    - Test response status
    - Test response body structure
    - Test error cases
    - Test authorization if applicable
    example: |
      RSpec.describe Api::OrdersController do
        describe "GET #index" do
          context "when authenticated" do
            before { sign_in user }

            it "returns success" do
              get :index
              expect(response).to have_http_status(:ok)
            end

            it "returns orders list" do
              get :index
              expect(json_response[:data]).to be_an(Array)
            end
          end

          context "when not authenticated" do
            it "returns unauthorized" do
              get :index
              expect(response).to have_http_status(:unauthorized)
            end
          end
        end
      end

  # Service Specs
  services:
    - Test main public methods
    - Test error handling
    - Use contexts for different scenarios
    - Mock external dependencies
    example: |
      RSpec.describe OrderService do
        describe "#create_order" do
          context "with valid params" do
            it "creates an order" do
              expect {
                service.create_order(valid_params)
              }.to change(Order, :count).by(1)
            end
          end

          context "with invalid params" do
            it "raises error" do
              expect {
                service.create_order(invalid_params)
              }.to raise_error(StandardError)
            end
          end
        end
      end
