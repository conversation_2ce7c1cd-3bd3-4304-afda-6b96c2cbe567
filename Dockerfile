FROM ruby:2.6.8-alpine

# Add all required dependencies for native extensions
RUN apk update && apk upgrade && apk add --no-cache \
    build-base \
    libxml2-dev libxslt-dev \
    postgresql-dev \
    yaml-dev zlib-dev \
    nodejs tzdata \
    bash git openssh curl imagemagick gcompat

WORKDIR /app

COPY Gemfile Gemfile.lock ./

# Improve bundler speed and verbosity to debug
RUN gem update --system 3.3.22 && \
    bundle config set jobs 4 && \
    bundle config set retry 2 && \
    bundle config set build.nokogiri "--use-system-libraries" && \
    bundle install --binstubs --verbose

COPY . .

ARG env
COPY .env.dev .env

CMD RAILS_ENV=production puma -C config/puma.rb