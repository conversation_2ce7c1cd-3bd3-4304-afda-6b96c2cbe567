# User Account Deletion - Client Implementation Guide

This guide provides practical examples for implementing user account deletion in your client application.

## API Endpoints

### 1. Check Account Deletion Eligibility

**Request:**
```http
GET /api/users/deletion_check
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Response - Always Can Delete:**
```json
{
  "can_delete": true,
  "orders_count": 2,
  "carts_count": 1,
  "message": "Account can be deleted. Orders and carts will be automatically removed."
}
```

**Response - No Data to Clean:**
```json
{
  "can_delete": true,
  "orders_count": 0,
  "carts_count": 0,
  "message": "Account can be deleted. Orders and carts will be automatically removed."
}
```

### 2. Delete User Account

**Request:**
```http
DELETE /api/users/account
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Response - Success:**
```json
{
  "message": "Account successfully deleted",
  "details": {
    "firebase_deleted": true,
    "shopify_deleted": true,
    "user_deleted": true,
    "orders_cleaned": true,
    "carts_cleaned": true
  }
}
```

**Response - Partial Failure:**
```json
{
  "error": "Account deletion failed",
  "details": {
    "firebase_deleted": true,
    "shopify_deleted": false,
    "user_deleted": false,
    "errors": ["Failed to delete user from local database"]
  },
  "message": "Some parts of the deletion process may have completed. Please contact support if needed."
}
```

### 3. Get User Profile

**Request:**
```http
GET /api/users/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Response:**
```json
{
  "user": {
    "id": "507f1f77bcf86cd799439011",
    "email": "<EMAIL>",
    "name": "John Doe",
    "shopify_customer_id": "*********",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

## Client Implementation Examples

### Flutter/Dart Example

```dart
class UserDeletionService {
  final String baseUrl;
  final String authToken;

  UserDeletionService({required this.baseUrl, required this.authToken});

  // Check deletion info (always returns can_delete: true)
  Future<Map<String, dynamic>> checkDeletionInfo() async {
    final response = await http.get(
      Uri.parse('$baseUrl/api/users/deletion_check'),
      headers: {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json',
      },
    );

    return json.decode(response.body);
  }

  // Delete user account
  Future<Map<String, dynamic>> deleteAccount() async {
    final response = await http.delete(
      Uri.parse('$baseUrl/api/users/account'),
      headers: {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json',
      },
    );

    return {
      'statusCode': response.statusCode,
      'body': json.decode(response.body),
    };
  }

  // Complete deletion flow
  Future<bool> performAccountDeletion() async {
    try {
      // Step 1: Get deletion info (optional - shows what will be cleaned)
      final checkResult = await checkDeletionInfo();
      print('Will clean ${checkResult['orders_count']} orders and ${checkResult['carts_count']} carts');

      // Step 2: Delete account (no validation needed - auto cleanup)
      final deleteResult = await deleteAccount();

      if (deleteResult['statusCode'] == 200) {
        print('Account deleted successfully');
        print('Cleaned orders: ${deleteResult['body']['details']['orders_cleaned']}');
        print('Cleaned carts: ${deleteResult['body']['details']['carts_cleaned']}');
        return true;
      } else {
        print('Deletion failed: ${deleteResult['body']['error']}');
        return false;
      }
    } catch (e) {
      print('Error during account deletion: $e');
      return false;
    }
  }
}
```

### JavaScript/React Example

```javascript
class UserDeletionAPI {
  constructor(baseUrl, authToken) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
  }

  async checkDeletionEligibility() {
    const response = await fetch(`${this.baseUrl}/api/users/deletion_check`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json',
      },
    });

    return await response.json();
  }

  async deleteAccount() {
    const response = await fetch(`${this.baseUrl}/api/users/account`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json',
      },
    });

    return {
      ok: response.ok,
      status: response.status,
      data: await response.json(),
    };
  }

  async performAccountDeletion() {
    try {
      // Step 1: Check eligibility
      const checkResult = await this.checkDeletionEligibility();
      
      if (!checkResult.can_delete) {
        console.log('Cannot delete account:', checkResult.errors);
        return { success: false, errors: checkResult.errors };
      }

      // Step 2: Confirm with user
      const confirmed = confirm(
        'Are you sure you want to delete your account? This action cannot be undone.'
      );
      if (!confirmed) {
        return { success: false, cancelled: true };
      }

      // Step 3: Delete account
      const deleteResult = await this.deleteAccount();
      
      if (deleteResult.ok) {
        console.log('Account deleted successfully');
        return { success: true, data: deleteResult.data };
      } else {
        console.error('Deletion failed:', deleteResult.data.error);
        return { success: false, error: deleteResult.data.error };
      }
    } catch (error) {
      console.error('Error during account deletion:', error);
      return { success: false, error: error.message };
    }
  }
}
```

## Error Handling Guide

### Common HTTP Status Codes

- **200 OK**: Account deleted successfully
- **422 Unprocessable Entity**: Validation failed or cannot delete
- **401 Unauthorized**: Invalid or missing authentication token
- **500 Internal Server Error**: Server error during deletion

### Client Error Handling

```dart
// Flutter example
Future<void> handleAccountDeletion() async {
  try {
    final result = await userDeletionService.performAccountDeletion();
    
    if (result) {
      // Success - redirect to goodbye page
      Navigator.pushReplacementNamed(context, '/goodbye');
    } else {
      // Show error message
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Deletion Failed'),
          content: Text('Unable to delete account. Please try again or contact support.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('OK'),
            ),
          ],
        ),
      );
    }
  } catch (e) {
    // Handle network or other errors
    print('Network error: $e');
  }
}
```

## UI Flow Recommendations

### 1. Account Settings Page
```
[Delete Account Button] -> Deletion Check -> Confirmation Dialog -> Delete -> Success/Error
```

### 2. Pre-deletion Checklist
- Show user what will be deleted
- Check for pending orders
- Check for cart items
- Provide clear warnings

### 3. Confirmation Dialog
```
"Are you sure you want to delete your account?

This will permanently:
• Delete your account from all systems
• Remove all your data
• Cancel any pending orders
• Log you out from all devices

This action cannot be undone."

[Cancel] [Delete Account]
```

### 4. Success/Error Messages
- **Success**: "Your account has been successfully deleted. Thank you for using our service."
- **Error**: "We couldn't delete your account right now. Please contact support for assistance."

## Testing Scenarios

1. **Happy Path**: User with no pending orders/cart items
2. **Blocked Deletion**: User with pending orders
3. **Partial Failure**: Firebase deleted but local cleanup fails
4. **Network Error**: Handle connection issues
5. **Invalid Token**: Handle authentication errors

This guide should help your client team implement the account deletion feature effectively!
