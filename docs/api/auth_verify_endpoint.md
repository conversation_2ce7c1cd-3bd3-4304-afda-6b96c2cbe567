# Auth Verify Token Endpoint

## Overview
The verify token endpoint allows clients to validate JWT tokens and retrieve current user information. This is useful for checking if a token is still valid and getting up-to-date user data.

## Endpoint Details

**URL:** `GET /api/auth/verify`

**Authentication:** Required (JWT Token in Authorization header)

**Content-Type:** `application/json`

## Request Headers

```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Response Format

### Success Response (200 OK)

```json
{
  "user": {
    "id": "user_id_string",
    "email": "<EMAIL>",
    "name": "User Name",
    "shopify_customer_id": "123456789",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

### Error Responses

#### Missing Token (422 Unprocessable Entity)
```json
{
  "error": "Authorization token required"
}
```

#### Invalid/Expired Token (422 Unprocessable Entity)
```json
{
  "error": "Invalid or expired token"
}
```

#### User Not Found (422 Unprocessable Entity)
```json
{
  "error": "User not found"
}
```

## Usage Examples

### cURL Example

```bash
# Valid token request
curl -X GET http://localhost:3000/api/auth/verify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..."

# Missing token request
curl -X GET http://localhost:3000/api/auth/verify \
  -H "Content-Type: application/json"

# Invalid token request
curl -X GET http://localhost:3000/api/auth/verify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer invalid.token.here"
```

### JavaScript/Fetch Example

```javascript
// Verify token
async function verifyToken(token) {
  try {
    const response = await fetch('/api/auth/verify', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('User data:', data.user);
      return data.user;
    } else {
      const error = await response.json();
      console.error('Token verification failed:', error.error);
      return null;
    }
  } catch (error) {
    console.error('Network error:', error);
    return null;
  }
}
```

### Flutter/Dart Example

```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

Future<Map<String, dynamic>?> verifyToken(String token) async {
  try {
    final response = await http.get(
      Uri.parse('http://localhost:3000/api/auth/verify'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['user'];
    } else {
      final error = json.decode(response.body);
      print('Token verification failed: ${error['error']}');
      return null;
    }
  } catch (e) {
    print('Network error: $e');
    return null;
  }
}
```

## Use Cases

1. **Token Validation**: Check if a stored JWT token is still valid before making API calls
2. **User Data Refresh**: Get updated user information without re-authenticating
3. **Session Management**: Verify user sessions in mobile apps or SPAs
4. **Authorization Checks**: Confirm user identity before accessing protected resources

## Implementation Notes

- The endpoint validates JWT tokens using the same secret as other authentication endpoints
- Token expiration is checked automatically during JWT decoding
- User existence is verified in the database
- Returns the same user data format as the authenticate endpoint
- No new token is generated - this is purely a verification endpoint

## Related Endpoints

- `POST /api/auth/authenticate` - Initial authentication with Firebase token
- Protected endpoints that use the `Authenticatable` concern for automatic token verification
