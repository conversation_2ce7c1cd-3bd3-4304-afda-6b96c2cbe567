# TravelGator API Documentation

## Overview

This documentation covers all API endpoints for the TravelGator simplified cart management platform, focusing on core cart item management and order history functionality.

### Base URL
```
Production: https://api.travelgator.com
Development: http://localhost:8889
```

### Authentication
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### ⚠️ Important: Endpoint URLs
All endpoints start with `/api/`. Make sure your client doesn't double the `/api/` prefix.

**Correct:** `GET https://api.travelgator.com/api/orders`
**Wrong:** `GET https://api.travelgator.com/api/api/orders`

## 🚀 Core Features

### Simplified Cart Management Platform
- **Streamlined cart management**: Add, update, and manage cart items
- **Order history**: Complete order history and status tracking
- **User management**: Authentication and profile management

### Clean API Design
- **Consistent responses**: Standardized JSON response format
- **Error handling**: Clear error messages and status codes
- **Performance optimized**: Fast response times and efficient data handling

## 📚 API Documentation

### Core APIs
- **[Cart Management](#cart-management)** - Cart operations and item management
- **[Order History](#order-history)** - Order management functionality

## Cart Management

### Get Cart
`GET /api/cart`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cart_123",
    "total_price": 150.0,
    "currency": "USD",
    "items": [
      {
        "variant_id": "variant_123",
        "product_id": "product_456",
        "quantity": 2,
        "price": 50.0,
        "title": "Premium Travel Backpack"
      }
    ]
  }
}
```

### Update Cart
`PUT /api/cart`

**Request:**
```json
{
  "items": [
    {
      "variant_id": "variant_123",
      "quantity": 2,
      "price": 50.0
    }
  ]
}
```

## Order History

### Get Orders
`GET /api/orders`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "60f7b*********0abcdef123",
      "total_price": 150.0,
      "currency": "USD",
      "status": "completed",
      "payment_status": "succeeded",
      "items": [
        {
          "variant_id": "*********",
          "quantity": 2,
          "price": 75.0,
          "title": "Premium eSIM - Europe"
        }
      ],
      "created_at": "2025-07-29T10:30:00.000Z"
    }
  ]
}
```

### Get Order Details
`GET /api/orders/:id`

**Response:** Same format as above for single order

### Check Order Status
`GET /api/orders/status?order_id=xxx&payment_method=xxx`

**Parameters:**
- `order_id` (required): The order ID to check status for
- `payment_method` (optional): Payment method used (currently supports: shopify)

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "completed",
    "payment_status": "paid",
    "order_id": "60f7b*********0abcdef123",
    "local_order_id": "60f7b*********0abcdef123",
    "shopify_order_name": "#1001",
    "total_price": 150.0,
    "currency": "USD",
    "completed_at": "2025-09-23T16:14:05.000Z",
    "order_completed": true
  }
}
```



## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": "Error message description"
}
```

### Common Error Messages

| Error | Solution |
|-------|----------|
| `"Invalid token"` | Re-authenticate |
| `"Order not found"` | Check order ID |
| `"Cart is empty"` | Add items to cart |


## Example Usage

### JavaScript
```javascript
// Get orders - ✅ Correct URL
const orders = await fetch('https://api.travelgator.com/api/orders', {
  headers: { 'Authorization': `Bearer ${token}` }
}).then(r => r.json());

// Update cart - ✅ Correct URL
const result = await fetch('https://api.travelgator.com/api/cart', {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ 
    items: [{ variant_id: 'variant_123', quantity: 2, price: 50.0 }] 
  })
}).then(r => r.json());
```

### Flutter
```dart
// Get orders
final response = await http.get(
  Uri.parse('$baseUrl/api/orders'),
  headers: {'Authorization': 'Bearer $token'},
);

// Update cart
final response = await http.put(
  Uri.parse('$baseUrl/api/cart'),
  headers: {
    'Authorization': 'Bearer $token',
    'Content-Type': 'application/json',
  },
  body: jsonEncode({
    'items': [{'variant_id': 'variant_123', 'quantity': 2, 'price': 50.0}]
  }),
);
```
