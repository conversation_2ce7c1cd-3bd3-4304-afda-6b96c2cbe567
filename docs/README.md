# TravelGator API Documentation

## Order History System

### Base URL
```
Production: https://api.travelgator.com
Development: http://localhost:3000
```

### ⚠️ Common Issue: Double /api/ in URLs
**Problem:** Getting `No route matches [GET] "/api/api/orders"`

**Cause:** Client is adding `/api/` twice in the URL

**Solution:** Use the correct URL format:
```javascript
// ✅ Correct
const response = await fetch('https://api.travelgator.com/api/orders');

// ❌ Wrong - Don't do this
const response = await fetch('https://api.travelgator.com/api/api/orders');
```

### Authentication
```http
Authorization: Bearer <jwt_token>
```

## Available APIs

### Order History
- `GET /api/orders` - Get user order history
- `GET /api/orders/:id` - Get specific order details
- `GET /api/orders/status?order_id=xxx&payment_method=xxx` - Check order payment status



## Quick Examples

### Get Orders
```javascript
const orders = await fetch('https://api.travelgator.com/api/orders', {
  headers: { 'Authorization': `Bearer ${token}` }
}).then(r => r.json());
```



## Error Handling

All APIs return:
```json
{
  "success": true/false,
  "data": {...} or "error": "message"
}
```

Common errors:
- `"Invalid token"` - Re-authenticate
- `"Order not found"` - Check order ID

---

For detailed API documentation, see [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)
