require "mongoid"
require "faker"

# Load Rails environment
require_relative "../config/environment"

# Set test environment before loading Rails or Mongoid config
ENV["RAILS_ENV"] ||= "test"

# Load Rails environment
require_relative "../config/environment"

# Load Mongoid config from mongoid.yml manually
Mongoid.load!(File.expand_path("../config/mongoid.yml", __dir__), ENV["RAILS_ENV"])

# Configure RSpec
RSpec.configure do |config|
  # suppress mongoid logging
  Mongo::Logger.logger.level = Logger::FATAL

  config.expect_with :rspec do |expectations|
    expectations.include_chain_clauses_in_custom_matcher_descriptions = true
  end

  config.mock_with :rspec do |mocks|
    mocks.verify_partial_doubles = true
  end

  config.shared_context_metadata_behavior = :apply_to_host_groups

  # Clean database between tests
  config.before(:each) do
    Mongoid.purge!
  end

  # Include Faker for test data generation
  config.include Faker

  # Configure test environment
  config.before(:suite) do
    # Set test environment
    ENV["RAILS_ENV"] = "test"
  end
end

# Helper methods for tests
module TestHelpers
  def create_user(attributes = {})
    User.create!({
      firebase_uid: Faker::Internet.unique.uuid,
      email: Faker::Internet.unique.email,
      name: Faker::Name.name,
      shopify_customer_id: Faker::Number.number(digits: 10).to_s,
    }.merge(attributes))
  end

  def create_cart(user, attributes = {})
    Cart.create!({
      user: user,
      status: "active",
    }.merge(attributes))
  end

  def create_order(user, attributes = {})
    Order.create!({
      user: user,
      shopify_order_id: Faker::Number.number(digits: 10).to_s,
      shopify_checkout_url: Faker::Internet.url,
      total_price: Faker::Commerce.price,
      currency: "USD",
      status: "pending",
      items: [],
    }.merge(attributes))
  end

  def mock_firebase_user
    {
      "localId" => Faker::Internet.unique.uuid,
      "email" => Faker::Internet.unique.email,
      "displayName" => Faker::Name.name,
    }
  end

  def mock_shopify_customer
    {
      "id" => Faker::Number.number(digits: 10).to_s,
      "email" => Faker::Internet.unique.email,
      "first_name" => Faker::Name.first_name,
      "last_name" => Faker::Name.last_name,
    }
  end

  def mock_shopify_checkout
    {
      "id" => Faker::Number.number(digits: 10).to_s,
      "webUrl" => Faker::Internet.url,
      "totalPrice" => {
        "amount" => Faker::Commerce.price.to_s,
        "currencyCode" => "USD",
      },
      "isDraft" => false,
      "name" => "Order ##{Faker::Number.number(digits: 6)}",
    }
  end

  def mock_cart_items
    [
      {
        "variant_id" => Faker::Number.number(digits: 10).to_s,
        "product_id" => Faker::Number.number(digits: 10).to_s,
        "quantity" => rand(1..5),
        "price" => Faker::Commerce.price,
        "title" => Faker::Commerce.product_name,
      },
    ]
  end
end

RSpec.configure do |config|
  config.include TestHelpers
end
