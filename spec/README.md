# Test Suite Documentation

This directory contains comprehensive RSpec tests for all services in the TravelGator backend project.

## Test Structure

### Services Tests
- `spec/services/auth_service_spec.rb` - Tests for authentication service
- `spec/services/cart_service_spec.rb` - Tests for cart management service
- `spec/services/firebase_auth_service_spec.rb` - Tests for Firebase authentication service
- `spec/services/shopify_service_spec.rb` - Tests for Shopify integration service

### Test Configuration
- `spec/spec_helper.rb` - RSpec configuration and test helpers

## Running Tests

### Prerequisites
1. Ensure MongoDB is running locally
2. Set up test environment variables (if needed for external service mocking)

### Run All Tests
```bash
bundle exec rspec
```

### Run Specific Service Tests
```bash
# Run auth service tests only
bundle exec rspec spec/services/auth_service_spec.rb

# Run cart service tests only
bundle exec rspec spec/services/cart_service_spec.rb

# Run Firebase auth service tests only
bundle exec rspec spec/services/firebase_auth_service_spec.rb

# Run Shopify service tests only
bundle exec rspec spec/services/shopify_service_spec.rb
```

### Run Tests with Coverage
```bash
bundle exec rspec --format documentation
```

## Test Coverage

### AuthService
- ✅ User authentication with Firebase tokens
- ✅ User creation and linking with Shopify customers
- ✅ JWT token generation
- ✅ Error handling for missing/invalid tokens
- ✅ Error handling for service failures

### CartService
- ✅ Cart creation and management
- ✅ Adding items to cart with validation
- ✅ Removing items from cart
- ✅ Updating item quantities
- ✅ Checkout processing with Shopify integration
- ✅ Error handling for invalid parameters
- ✅ Error handling for service failures

### FirebaseAuthService
- ✅ Firebase token verification
- ✅ User data retrieval by UID and email
- ✅ Custom token creation
- ✅ Token validation (expiration, issuer, audience, subject)
- ✅ Public key fetching and validation
- ✅ Error handling for invalid tokens
- ✅ Error handling for API failures

### ShopifyService
- ✅ Customer creation and management
- ✅ Customer search by email
- ✅ Checkout creation (draft orders and carts)
- ✅ GraphQL and REST API integration
- ✅ Error handling for API failures
- ✅ Configuration validation

## Test Patterns

### Mocking External Services
All external service calls are mocked to ensure tests are fast and reliable:

```ruby
# Example: Mocking Firebase Auth Service
let(:firebase_auth_service) { instance_double(FirebaseAuthService) }
allow(firebase_auth_service).to receive(:verify_token).and_return(mock_firebase_user)
```

### Test Data Generation
Using Faker for generating realistic test data:

```ruby
# Example: Creating test users
let(:user) { create_user(firebase_uid: "test_uid", email: "<EMAIL>") }
```

### Error Testing
Comprehensive error testing for all failure scenarios:

```ruby
# Example: Testing validation errors
it "raises error for missing parameters" do
  expect {
    service.add_item(invalid_params)
  }.to raise_error("Missing required parameters")
end
```

### Database Cleanup
Tests automatically clean the database between runs:

```ruby
config.before(:each) do
  Mongoid.purge!
end
```

## Test Helpers

The `spec/spec_helper.rb` file includes helper methods for:

- Creating test users, carts, and orders
- Generating mock data for external services
- Setting up test environment
- Database cleanup

## Best Practices Followed

1. **Descriptive Contexts**: Each test group has clear, descriptive contexts
2. **Focused Tests**: Each test focuses on a single behavior
3. **Arrange-Act-Assert**: Tests follow the AAA pattern
4. **Mocking**: External dependencies are properly mocked
5. **Error Testing**: Both happy and unhappy paths are tested
6. **Fast Execution**: Tests run quickly with proper mocking
7. **Clear Expectations**: Test expectations are clear and specific

## Continuous Integration

These tests are designed to run in CI/CD pipelines and will:
- Validate all service functionality
- Ensure error handling works correctly
- Verify integration points are properly tested
- Maintain code quality standards

## Troubleshooting

### Common Issues

1. **MongoDB Connection**: Ensure MongoDB is running on localhost:27017
2. **Environment Variables**: Some tests may require environment variables for external services
3. **Dependencies**: Ensure all gems are installed with `bundle install`

### Debugging Tests

To debug failing tests:

```bash
# Run with verbose output
bundle exec rspec --format documentation

# Run specific failing test
bundle exec rspec spec/services/auth_service_spec.rb:25

# Run with debugger
bundle exec rspec --require pry spec/services/auth_service_spec.rb
```