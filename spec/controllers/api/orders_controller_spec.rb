require 'spec_helper'

RSpec.describe Api::OrdersController, type: :controller do
  let(:user) { create(:user) }
  let(:order) { create(:order, user: user, total_price: 90.0, original_total: 100.0) }

  before do
    allow(controller).to receive(:authenticate_user!).and_return(true)
    allow(controller).to receive(:current_user).and_return(user)
    controller.instance_variable_set(:@current_user, user)
  end

  describe "GET #index" do
    let!(:orders) { create_list(:order, 3, user: user) }

    it "returns user's orders" do
      get :index
      
      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['data'].length).to eq(3)
    end


  end

  describe "GET #show" do
    it "returns specific order" do
      get :show, params: { id: order.id }
      
      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['data']['id']).to eq(order.id.to_s)
    end



    it "returns error for non-existent order" do
      get :show, params: { id: "nonexistent" }
      
      expect(response).to have_http_status(:unprocessable_entity)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be false
    end

    it "returns error for other user's order" do
      other_user = create(:user)
      other_order = create(:order, user: other_user)
      
      get :show, params: { id: other_order.id }
      
      expect(response).to have_http_status(:unprocessable_entity)
    end
  end




end
