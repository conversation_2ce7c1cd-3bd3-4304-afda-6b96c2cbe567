require "spec_helper"
require "rails_helper" if File.exist?(File.expand_path("../rails_helper.rb", __dir__))

RSpec.describe Api::AuthController, type: :request do
  let(:user) { create_user }
  let(:valid_token) { generate_jwt_token(user) }
  let(:invalid_token) { "invalid.jwt.token" }
  let(:expired_token) { generate_expired_jwt_token(user) }

  describe "GET /api/auth/verify" do
    context "with valid token" do
      let(:headers) { { "Authorization" => "Bearer #{valid_token}" } }

      it "returns user data" do
        get "/api/auth/verify", headers: headers

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response["user"]).to include(
          "id" => user.id.to_s,
          "email" => user.email,
          "name" => user.name,
          "shopify_customer_id" => user.shopify_customer_id
        )
      end

      it "includes timestamps" do
        get "/api/auth/verify", headers: headers

        json_response = JSON.parse(response.body)
        expect(json_response["user"]).to have_key("created_at")
        expect(json_response["user"]).to have_key("updated_at")
      end
    end

    context "with missing token" do
      it "returns authorization required error" do
        get "/api/auth/verify"

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("Authorization token required")
      end
    end

    context "with invalid token" do
      let(:headers) { { "Authorization" => "Bearer #{invalid_token}" } }

      it "returns invalid token error" do
        get "/api/auth/verify", headers: headers

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("Invalid or expired token")
      end
    end

    context "with expired token" do
      let(:headers) { { "Authorization" => "Bearer #{expired_token}" } }

      it "returns invalid token error" do
        get "/api/auth/verify", headers: headers

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("Invalid or expired token")
      end
    end

    context "with token for non-existent user" do
      let(:non_existent_user_token) do
        JWT.encode(
          { user_id: "507f1f77bcf86cd799439011", exp: 24.hours.from_now.to_i },
          ENV["JWT_SECRET"] || "development_jwt_secret"
        )
      end
      let(:headers) { { "Authorization" => "Bearer #{non_existent_user_token}" } }

      it "returns user not found error" do
        get "/api/auth/verify", headers: headers

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("User not found")
      end
    end
  end

  describe "JWT Token Verification Logic" do
    it "can decode a valid token" do
      decoded_token = JWT.decode(valid_token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' })
      user_data = decoded_token[0]

      expect(user_data['user_id']).to eq(user.id.to_s)
      expect(user_data['exp']).to be > Time.now.to_i
    end

    it "raises error for expired token" do
      expect {
        JWT.decode(expired_token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' })
      }.to raise_error(JWT::ExpiredSignature)
    end

    it "raises error for invalid token" do
      expect {
        JWT.decode(invalid_token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' })
      }.to raise_error(JWT::DecodeError)
    end

    it "can find user from valid token" do
      decoded_token = JWT.decode(valid_token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' })
      user_data = decoded_token[0]
      found_user = User.find(user_data['user_id'])

      expect(found_user.id).to eq(user.id)
      expect(found_user.email).to eq(user.email)
      expect(found_user.name).to eq(user.name)
      expect(found_user.firebase_uid).to eq(user.firebase_uid)
    end
  end

  describe "Auth controller helper methods" do
    let(:controller) { Api::AuthController.new }

    describe "#extract_token_from_header" do
      it "extracts token from Bearer authorization header" do
        mock_request = double('request')
        headers = { 'Authorization' => 'Bearer test_token_123' }
        allow(mock_request).to receive(:headers).and_return(headers)
        allow(controller).to receive(:request).and_return(mock_request)

        token = controller.send(:extract_token_from_header)
        expect(token).to eq('test_token_123')
      end

      it "returns nil when no Authorization header" do
        mock_request = double('request')
        headers = {}
        allow(mock_request).to receive(:headers).and_return(headers)
        allow(controller).to receive(:request).and_return(mock_request)

        token = controller.send(:extract_token_from_header)
        expect(token).to be_nil
      end

      it "returns nil when Authorization header doesn't start with Bearer" do
        mock_request = double('request')
        headers = { 'Authorization' => 'Basic test_token_123' }
        allow(mock_request).to receive(:headers).and_return(headers)
        allow(controller).to receive(:request).and_return(mock_request)

        token = controller.send(:extract_token_from_header)
        expect(token).to be_nil
      end
    end
  end

  private

  def generate_jwt_token(user)
    JWT.encode(
      { user_id: user.id.to_s, exp: 24.hours.from_now.to_i },
      ENV["JWT_SECRET"] || "development_jwt_secret"
    )
  end

  def generate_expired_jwt_token(user)
    JWT.encode(
      { user_id: user.id.to_s, exp: 1.hour.ago.to_i },
      ENV["JWT_SECRET"] || "development_jwt_secret"
    )
  end
end
