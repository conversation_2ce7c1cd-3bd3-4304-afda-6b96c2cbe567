require "spec_helper"
require "rails_helper" if File.exist?(File.expand_path("../rails_helper.rb", __dir__))

RSpec.describe Api::UsersController, type: :request do
  let(:user) { create_user }
  let(:valid_token) { generate_jwt_token(user) }
  let(:headers) { { "Authorization" => "Bearer #{valid_token}" } }

  describe "GET /api/users/deletion_check" do
    context "when user can be deleted" do
      before do
        allow_any_instance_of(UserDeletionService).to receive(:can_delete?).and_return({ can_delete: true })
      end

      it "returns success response" do
        get "/api/users/deletion_check", headers: headers

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response["can_delete"]).to be true
        expect(json_response["message"]).to eq("Account can be safely deleted")
      end
    end

    context "when user cannot be deleted" do
      before do
        allow_any_instance_of(UserDeletionService).to receive(:can_delete?).and_return({
          can_delete: false,
          errors: ["Cannot delete account with pending orders"]
        })
      end

      it "returns error response" do
        get "/api/users/deletion_check", headers: headers

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response["can_delete"]).to be false
        expect(json_response["errors"]).to include("Cannot delete account with pending orders")
      end
    end

    context "without authentication" do
      it "returns unauthorized error" do
        get "/api/users/deletion_check"

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("Authorization required")
      end
    end
  end

  describe "DELETE /api/users/account" do
    context "when deletion is successful" do
      before do
        allow_any_instance_of(UserDeletionService).to receive(:can_delete?).and_return({ can_delete: true })
        allow_any_instance_of(UserDeletionService).to receive(:delete_account).and_return({
          user_deleted: true,
          firebase_deleted: true,
          shopify_deleted: true,
          errors: []
        })
      end

      it "deletes the account successfully" do
        delete "/api/users/account", headers: headers

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response["message"]).to eq("Account successfully deleted")
        expect(json_response["details"]["user_deleted"]).to be true
        expect(json_response["details"]["firebase_deleted"]).to be true
        expect(json_response["details"]["shopify_deleted"]).to be true
      end
    end

    context "when user cannot be deleted" do
      before do
        allow_any_instance_of(UserDeletionService).to receive(:can_delete?).and_return({
          can_delete: false,
          errors: ["Cannot delete account with pending orders"]
        })
      end

      it "returns validation error" do
        delete "/api/users/account", headers: headers

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("Cannot delete account")
        expect(json_response["reasons"]).to include("Cannot delete account with pending orders")
      end
    end

    context "without authentication" do
      it "returns unauthorized error" do
        delete "/api/users/account"

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("Authorization required")
      end
    end
  end

  describe "GET /api/users/profile" do
    context "with valid authentication" do
      it "returns user profile" do
        get "/api/users/profile", headers: headers

        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response["user"]).to include(
          "id" => user.id.to_s,
          "email" => user.email,
          "name" => user.name,
          "shopify_customer_id" => user.shopify_customer_id
        )
      end
    end

    context "without authentication" do
      it "returns unauthorized error" do
        get "/api/users/profile"

        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to eq("Authorization required")
      end
    end
  end

  private

  def create_user
    User.new(
      id: BSON::ObjectId.new,
      firebase_uid: "firebase_123",
      email: "<EMAIL>",
      name: "Test User",
      shopify_customer_id: "shopify_456",
      created_at: Time.current,
      updated_at: Time.current
    )
  end

  def generate_jwt_token(user)
    JWT.encode(
      { user_id: user.id.to_s, exp: 24.hours.from_now.to_i },
      ENV["JWT_SECRET"] || "development_jwt_secret"
    )
  end
end
