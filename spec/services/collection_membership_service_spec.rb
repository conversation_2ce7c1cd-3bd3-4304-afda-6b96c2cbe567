require 'spec_helper'

RSpec.describe CollectionMembershipService, type: :service do
  let(:service) { CollectionMembershipService.new }
  let(:collection_id) { "gid://shopify/Collection/123456" }
  let(:product_ids) { ["product_1", "product_2", "product_3"] }

  before do
    # Clear cache before each test
    Rails.cache.clear
  end

  describe "#get_collection_products" do
    let(:mock_response) do
      double(
        data: double(
          collection: double(
            id: collection_id,
            title: "Travel Gear",
            products: double(
              edges: [
                double(node: double(legacyResourceId: "product_1", status: "ACTIVE")),
                double(node: double(legacyResourceId: "product_2", status: "ACTIVE")),
                double(node: double(legacyResourceId: "product_3", status: "DRAFT"))  # Should be excluded
              ],
              pageInfo: double(hasNextPage: false, endCursor: nil)
            )
          )
        )
      )
    end

    before do
      client = double("GraphQL Client")
      allow(service).to receive(:create_shopify_client).and_return(client)
      allow(client).to receive(:query).and_return(mock_response)
    end

    it "fetches and caches collection products" do
      products = service.get_collection_products("123456")
      
      expect(products).to eq(["product_1", "product_2"])  # Only active products
      
      # Check that result is cached
      cached_products = Rails.cache.read("collection_products_123456")
      expect(cached_products).to eq(["product_1", "product_2"])
    end

    it "returns cached products when available" do
      # Pre-populate cache
      Rails.cache.write("collection_products_123456", ["cached_product_1", "cached_product_2"])
      
      products = service.get_collection_products("123456")
      
      expect(products).to eq(["cached_product_1", "cached_product_2"])
    end

    it "bypasses cache when use_cache is false" do
      # Pre-populate cache
      Rails.cache.write("collection_products_123456", ["cached_product"])
      
      products = service.get_collection_products("123456", use_cache: false)
      
      expect(products).to eq(["product_1", "product_2"])  # Fresh data, not cached
    end
  end

  describe "#get_products_in_collections" do
    before do
      # Mock collection products
      allow(service).to receive(:get_collection_products).with("123456").and_return(["product_1", "product_2"])
      allow(service).to receive(:get_collection_products).with("789012").and_return(["product_2", "product_3"])
    end

    it "returns products that belong to any of the specified collections" do
      collection_ids = ["gid://shopify/Collection/123456", "gid://shopify/Collection/789012"]
      
      products = service.get_products_in_collections(collection_ids)
      
      expect(products).to contain_exactly("product_1", "product_2", "product_3")
    end

    it "filters to only requested products when product_ids provided" do
      collection_ids = ["gid://shopify/Collection/123456", "gid://shopify/Collection/789012"]
      requested_products = ["product_1", "product_4"]  # product_4 not in any collection
      
      products = service.get_products_in_collections(collection_ids, requested_products)
      
      expect(products).to eq(["product_1"])  # Only product_1 is both requested and in collections
    end

    it "returns empty array for empty collection_ids" do
      products = service.get_products_in_collections([])
      expect(products).to eq([])
    end
  end

  describe "#products_in_collections?" do
    before do
      allow(service).to receive(:get_collection_memberships).and_return(["product_1", "product_2"])
    end

    it "returns true when products belong to collections" do
      result = service.products_in_collections?(["product_1", "product_3"], ["gid://shopify/Collection/123456"])
      expect(result).to be true
    end

    it "returns false when no products belong to collections" do
      allow(service).to receive(:get_collection_memberships).and_return([])
      
      result = service.products_in_collections?(["product_4", "product_5"], ["gid://shopify/Collection/123456"])
      expect(result).to be false
    end

    it "returns false for empty inputs" do
      expect(service.products_in_collections?([], ["gid://shopify/Collection/123456"])).to be false
      expect(service.products_in_collections?(["product_1"], [])).to be false
    end
  end

  describe "#bulk_check_memberships" do
    before do
      allow(service).to receive(:get_collection_products).with("123456").and_return(["product_1", "product_2"])
      allow(service).to receive(:get_collection_products).with("789012").and_return(["product_2", "product_3"])
    end

    it "returns membership mapping for all products and collections" do
      collection_ids = ["gid://shopify/Collection/123456", "gid://shopify/Collection/789012"]
      product_ids = ["product_1", "product_2", "product_3", "product_4"]
      
      memberships = service.bulk_check_memberships(collection_ids, product_ids)
      
      expect(memberships).to eq({
        "product_1" => ["gid://shopify/Collection/123456"],
        "product_2" => ["gid://shopify/Collection/123456", "gid://shopify/Collection/789012"],
        "product_3" => ["gid://shopify/Collection/789012"],
        "product_4" => []
      })
    end

    it "returns empty hash for empty inputs" do
      expect(service.bulk_check_memberships([], ["product_1"])).to eq({})
      expect(service.bulk_check_memberships(["gid://shopify/Collection/123456"], [])).to eq({})
    end
  end

  describe "#get_collection_info" do
    let(:mock_response) do
      double(
        data: double(
          collection: double(
            id: collection_id,
            title: "Travel Gear",
            description: "Essential travel accessories",
            handle: "travel-gear",
            productsCount: 25,
            updatedAt: "2023-12-01T10:00:00Z"
          )
        )
      )
    end

    before do
      client = double("GraphQL Client")
      allow(service).to receive(:create_shopify_client).and_return(client)
      allow(client).to receive(:query).and_return(mock_response)
    end

    it "fetches and caches collection information" do
      info = service.get_collection_info(collection_id)
      
      expect(info).to eq({
        id: collection_id,
        title: "Travel Gear",
        description: "Essential travel accessories",
        handle: "travel-gear",
        products_count: 25,
        updated_at: "2023-12-01T10:00:00Z"
      })
      
      # Check that result is cached
      cached_info = Rails.cache.read("collection_info_123456")
      expect(cached_info).to eq(info)
    end
  end

  describe "#clear_collection_cache" do
    before do
      Rails.cache.write("collection_products_123456", ["product_1"])
      Rails.cache.write("collection_info_123456", { title: "Test" })
      Rails.cache.write("collection_products_789012", ["product_2"])
    end

    it "clears cache for specific collection" do
      service.clear_collection_cache("gid://shopify/Collection/123456")
      
      expect(Rails.cache.read("collection_products_123456")).to be_nil
      expect(Rails.cache.read("collection_info_123456")).to be_nil
      expect(Rails.cache.read("collection_products_789012")).not_to be_nil  # Other collection unchanged
    end

    it "clears all collection caches when no collection specified" do
      service.clear_collection_cache
      
      expect(Rails.cache.read("collection_products_123456")).to be_nil
      expect(Rails.cache.read("collection_info_123456")).to be_nil
      expect(Rails.cache.read("collection_products_789012")).to be_nil
    end
  end

  describe "#extract_shopify_id" do
    it "extracts ID from GraphQL format" do
      graphql_id = "gid://shopify/Collection/123456"
      extracted_id = service.send(:extract_shopify_id, graphql_id)
      expect(extracted_id).to eq("123456")
    end

    it "handles already extracted IDs" do
      simple_id = "123456"
      extracted_id = service.send(:extract_shopify_id, simple_id)
      expect(extracted_id).to eq("123456")
    end
  end

  describe "error handling" do
    before do
      client = double("GraphQL Client")
      allow(service).to receive(:create_shopify_client).and_return(client)
      allow(client).to receive(:query).and_raise(StandardError.new("API Error"))
    end

    it "handles API errors gracefully in get_collection_products" do
      products = service.get_collection_products("123456")
      expect(products).to eq([])
    end

    it "handles API errors gracefully in get_collection_info" do
      info = service.get_collection_info(collection_id)
      expect(info).to be_nil
    end
  end

  describe "pagination handling" do
    let(:first_page_response) do
      double(
        data: double(
          collection: double(
            products: double(
              edges: [
                double(node: double(legacyResourceId: "product_1", status: "ACTIVE")),
                double(node: double(legacyResourceId: "product_2", status: "ACTIVE"))
              ],
              pageInfo: double(hasNextPage: true, endCursor: "cursor_123")
            )
          )
        )
      )
    end

    let(:second_page_response) do
      double(
        data: double(
          collection: double(
            products: double(
              edges: [
                double(node: double(legacyResourceId: "product_3", status: "ACTIVE"))
              ],
              pageInfo: double(hasNextPage: false, endCursor: nil)
            )
          )
        )
      )
    end

    before do
      client = double("GraphQL Client")
      allow(service).to receive(:create_shopify_client).and_return(client)
      allow(client).to receive(:query)
        .with(anything, hash_including(after: nil))
        .and_return(first_page_response)
      allow(client).to receive(:query)
        .with(anything, hash_including(after: "cursor_123"))
        .and_return(second_page_response)
    end

    it "handles pagination correctly" do
      products = service.get_collection_products("123456", use_cache: false)
      expect(products).to eq(["product_1", "product_2", "product_3"])
    end
  end
end
