require "spec_helper"

RSpec.describe ShopifyService do
  let(:service) { ShopifyService.new }
  let(:user_data) { { email: "<EMAIL>", name: "Test User" } }
  let(:cart_items) { mock_cart_items }
  let(:customer_id) { "123456789" }

  # Mock responses
  let(:mock_customer_response) do
    {
      "customer" => {
        "id" => "123456789",
        "email" => "<EMAIL>",
        "first_name" => "Test",
        "last_name" => "User",
      },
    }
  end

  let(:mock_customers_search_response) do
    {
      "customers" => [
        {
          "id" => "123456789",
          "email" => "<EMAIL>",
          "first_name" => "Test",
          "last_name" => "User",
        },
      ],
    }
  end

  let(:mock_draft_order_response) do
    {
      "data" => {
        "draftOrderCreate" => {
          "draftOrder" => {
            "id" => "gid://shopify/DraftOrder/123456789",
            "invoiceUrl" => "https://example.com/invoice",
            "totalPrice" => "29.99",
            "currencyCode" => "USD",
          },
          "userErrors" => [],
        },
      },
    }
  end

  let(:mock_cart_response) do
    {
      "data" => {
        "cartCreate" => {
          "cart" => {
            "id" => "gid://shopify/Cart/123456789",
            "checkoutUrl" => "https://example.com/checkout",
            "cost" => {
              "totalAmount" => {
                "amount" => "29.99",
                "currencyCode" => "USD",
              },
            },
          },
          "userErrors" => [],
        },
      },
    }
  end

  before do
    # Mock environment variables
    allow(ENV).to receive(:[]).with("SHOPIFY_SHOP_NAME").and_return("test-shop")
    allow(ENV).to receive(:[]).with("SHOPIFY_ACCESS_TOKEN").and_return("test-token")
  end

  describe "#create_customer" do
    context "with valid user data" do
      before do
        allow(service).to receive(:find_customer_by_email).with(user_data[:email]).and_return(nil)
        allow(service).to receive(:make_request).with("POST", "/customers.json", anything).and_return(mock_customer_response)
      end

      it "creates a new customer" do
        result = service.create_customer(user_data)

        expect(result).to include(
          "id" => "123456789",
          "email" => "<EMAIL>",
          "first_name" => "Test",
          "last_name" => "User",
        )
      end

      it "builds correct customer data" do
        expect(service).to receive(:make_request).with("POST", "/customers.json", {
          customer: {
            email: "<EMAIL>",
            first_name: "Test",
            last_name: "User",
          },
        }).and_return(mock_customer_response)

        service.create_customer(user_data)
      end

      it "handles name with single word" do
        single_name_data = { email: "<EMAIL>", name: "Test" }

        expect(service).to receive(:make_request).with("POST", "/customers.json", {
          customer: {
            email: "<EMAIL>",
            first_name: "Test",
            last_name: "Test",
          },
        }).and_return(mock_customer_response)

        service.create_customer(single_name_data)
      end
    end

    context "when customer already exists" do
      let(:existing_customer) { mock_customers_search_response["customers"].first }

      before do
        allow(service).to receive(:find_customer_by_email).with(user_data[:email]).and_return(existing_customer)
      end

      it "returns existing customer" do
        result = service.create_customer(user_data)

        expect(result).to eq(existing_customer)
      end

      it "does not make create request" do
        expect(service).not_to receive(:make_request).with("POST", "/customers.json", anything)

        service.create_customer(user_data)
      end
    end

    context "when customer creation fails due to duplicate email" do
      before do
        allow(service).to receive(:find_customer_by_email).with(user_data[:email]).and_return(nil)
        allow(service).to receive(:make_request).with("POST", "/customers.json", anything).and_raise("API error: 422 - email already exists")
        allow(service).to receive(:find_customer_by_email).with(user_data[:email]).and_return(mock_customers_search_response["customers"].first)
      end

      it "falls back to finding existing customer" do
        result = service.create_customer(user_data)

        expect(result).to eq(mock_customers_search_response["customers"].first)
      end
    end

    context "with invalid user data" do
      it "raises error for missing user data" do
        expect {
          service.create_customer(nil)
        }.to raise_error("Failed to create customer: Missing user data")
      end

      it "raises error for missing email" do
        expect {
          service.create_customer({ name: "Test User" })
        }.to raise_error("Failed to create customer: Missing email")
      end

      it "raises error for blank email" do
        expect {
          service.create_customer({ email: "", name: "Test User" })
        }.to raise_error("Failed to create customer: Missing email")
      end
    end

    context "when API request fails" do
      before do
        allow(service).to receive(:find_customer_by_email).with(user_data[:email]).and_return(nil)
        allow(service).to receive(:make_request).with("POST", "/customers.json", anything).and_raise("API error: 500")
      end

      it "raises error" do
        expect {
          service.create_customer(user_data)
        }.to raise_error("Failed to create customer: API error: 500")
      end
    end
  end

  describe "#find_customer_by_email" do
    context "with valid email" do
      before do
        allow(service).to receive(:make_request).with("GET", "/customers/search.json?query=email:test%40example.com").and_return(mock_customers_search_response)
      end

      it "returns customer when found" do
        result = service.find_customer_by_email("<EMAIL>")

        expect(result).to eq(mock_customers_search_response["customers"].first)
      end

      it "returns nil when customer not found" do
        allow(service).to receive(:make_request).with("GET", "/customers/search.json?query=email:test%40example.com").and_return({ "customers" => [] })

        result = service.find_customer_by_email("<EMAIL>")

        expect(result).to be_nil
      end
    end

    context "with invalid email" do
      it "raises error for missing email" do
        expect {
          service.find_customer_by_email(nil)
        }.to raise_error("Failed to find customer: Missing email")
      end

      it "raises error for blank email" do
        expect {
          service.find_customer_by_email("")
        }.to raise_error("Failed to find customer: Missing email")
      end
    end

    context "when API request fails" do
      before do
        allow(service).to receive(:make_request).with("GET", "/customers/search.json?query=email:test%40example.com").and_raise("API error: 500")
      end

      it "raises error" do
        expect {
          service.find_customer_by_email("<EMAIL>")
        }.to raise_error("Failed to find customer: API error: 500")
      end
    end
  end



  describe "private methods" do
    describe "#validate_configuration" do
      context "when configuration is valid" do
        it "does not raise error" do
          expect {
            service.send(:validate_configuration)
          }.not_to raise_error
        end
      end

      context "when shop name is missing" do
        before do
          allow(ENV).to receive(:[]).with("SHOPIFY_SHOP_NAME").and_return(nil)
        end

        it "raises error" do
          expect {
            service.send(:validate_configuration)
          }.to raise_error("Missing Shopify configuration")
        end
      end

      context "when access token is missing" do
        before do
          allow(ENV).to receive(:[]).with("SHOPIFY_ACCESS_TOKEN").and_return(nil)
        end

        it "raises error" do
          expect {
            service.send(:validate_configuration)
          }.to raise_error("Missing Shopify configuration")
        end
      end
    end

    describe "#create_draft_order" do
      before do
        allow(service).to receive(:make_admin_request).and_return(mock_draft_order_response)
      end

      it "creates draft order successfully" do
        result = service.send(:create_draft_order, cart_items, customer_id)

        expect(result).to include(
          "id" => "gid://shopify/DraftOrder/123456789",
          "invoiceUrl" => "https://example.com/invoice",
          "totalPrice" => "29.99",
          "currencyCode" => "USD",
        )
      end

      it "builds correct variables" do
        expect(service).to receive(:make_admin_request).with(
          anything,
          {
            input: {
              lineItems: [
                {
                  variantId: "gid://shopify/ProductVariant/#{cart_items.first["variant_id"]}",
                  quantity: cart_items.first["quantity"],
                },
              ],
              customerId: "gid://shopify/Customer/#{customer_id}",
            },
          }
        ).and_return(mock_draft_order_response)

        service.send(:create_draft_order, cart_items, customer_id)
      end

      it "handles variant_id with gid prefix" do
        items_with_gid = cart_items.map { |item| item.merge("variant_id" => "gid://shopify/ProductVariant/123456") }

        expect(service).to receive(:make_admin_request).with(
          anything,
          {
            input: {
              lineItems: [
                {
                  variantId: "gid://shopify/ProductVariant/123456",
                  quantity: cart_items.first["quantity"],
                },
              ],
              customerId: "gid://shopify/Customer/#{customer_id}",
            },
          }
        ).and_return(mock_draft_order_response)

        service.send(:create_draft_order, items_with_gid, customer_id)
      end

      context "when API request fails" do
        before do
          allow(service).to receive(:make_admin_request).and_raise("API error")
        end

        it "raises error" do
          expect {
            service.send(:create_draft_order, cart_items, customer_id)
          }.to raise_error("Failed to create draft order: API error")
        end
      end

      context "when response has user errors" do
        let(:error_response) do
          {
            "data" => {
              "draftOrderCreate" => {
                "draftOrder" => nil,
                "userErrors" => [
                  { "field" => "lineItems", "message" => "Invalid variant" },
                ],
              },
            },
          }
        end

        before do
          allow(service).to receive(:make_admin_request).and_return(error_response)
        end

        it "returns nil" do
          result = service.send(:create_draft_order, cart_items, customer_id)
          expect(result).to be_nil
        end
      end
    end

    describe "#create_storefront_cart" do
      before do
        allow(service).to receive(:make_storefront_request).and_return(mock_cart_response)
      end

      it "creates cart successfully" do
        result = service.send(:create_storefront_cart, cart_items, customer_id)

        expect(result).to include(
          "id" => "gid://shopify/Cart/123456789",
          "webUrl" => "https://example.com/checkout",
          "totalPrice" => {
            "amount" => "29.99",
            "currencyCode" => "USD",
          },
        )
      end

      it "builds correct variables" do
        expect(service).to receive(:make_storefront_request).with(
          anything,
          {
            input: {
              lines: [
                {
                  merchandiseId: "gid://shopify/ProductVariant/#{cart_items.first["variant_id"]}",
                  quantity: cart_items.first["quantity"],
                },
              ],
            },
          }
        ).and_return(mock_cart_response)

        service.send(:create_storefront_cart, cart_items, customer_id)
      end

      context "when API request fails" do
        before do
          allow(service).to receive(:make_storefront_request).and_raise("API error")
        end

        it "raises error" do
          expect {
            service.send(:create_storefront_cart, cart_items, customer_id)
          }.to raise_error("Failed to create cart: API error")
        end
      end
    end

    describe "#make_request" do
      let(:mock_response) { double("response", code: "200", body: mock_customer_response.to_json) }
      let(:http_double) do
        double("http").tap do |http|
          allow(http).to receive(:use_ssl=)
          allow(http).to receive(:read_timeout=)
          allow(http).to receive(:open_timeout=)
          allow(http).to receive(:request).and_return(mock_response)
        end
      end

      before do
        allow(Net::HTTP).to receive(:new).and_return(http_double)
        allow(Net::HTTP::Post).to receive(:new).and_return(double("request").tap { |req| allow(req).to receive(:[]=); allow(req).to receive(:body=) })
      end

      it "makes successful request" do
        result = service.send(:make_request, "POST", "/customers.json", user_data)

        expect(result).to eq(mock_customer_response)
      end

      context "when API returns error" do
        let(:error_response) { double("response", code: "422", body: "Validation error") }
        let(:http_double_error) do
          double("http").tap do |http|
            allow(http).to receive(:use_ssl=)
            allow(http).to receive(:read_timeout=)
            allow(http).to receive(:open_timeout=)
            allow(http).to receive(:request).and_return(error_response)
          end
        end
        before do
          allow(Net::HTTP).to receive(:new).and_return(http_double_error)
          allow(Net::HTTP::Post).to receive(:new).and_return(double("request").tap { |req| allow(req).to receive(:[]=); allow(req).to receive(:body=) })
        end
        it "raises error" do
          expect {
            service.send(:make_request, "POST", "/customers.json", user_data)
          }.to raise_error("Request failed: API error: 422 - Validation error")
        end
      end

      context "when response is invalid JSON" do
        let(:invalid_response) { double("response", code: "200", body: "invalid json") }
        let(:http_double_invalid) do
          double("http").tap do |http|
            allow(http).to receive(:use_ssl=)
            allow(http).to receive(:read_timeout=)
            allow(http).to receive(:open_timeout=)
            allow(http).to receive(:request).and_return(invalid_response)
          end
        end
        before do
          allow(Net::HTTP).to receive(:new).and_return(http_double_invalid)
          allow(Net::HTTP::Post).to receive(:new).and_return(double("request").tap { |req| allow(req).to receive(:[]=); allow(req).to receive(:body=) })
        end
        it "raises error" do
          expect {
            service.send(:make_request, "POST", "/customers.json", user_data)
          }.to raise_error(/Invalid JSON response: /)
        end
      end
    end

    describe "#make_graphql_request" do
      let(:query) { "mutation { test }" }
      let(:variables) { { input: { test: "value" } } }
      let(:mock_response) { double("response", code: "200", body: { data: { test: "result" } }.to_json) }
      let(:http_double) do
        double("http").tap do |http|
          allow(http).to receive(:use_ssl=)
          allow(http).to receive(:read_timeout=)
          allow(http).to receive(:open_timeout=)
          allow(http).to receive(:request).and_return(mock_response)
        end
      end
      before do
        allow(Net::HTTP).to receive(:new).and_return(http_double)
        allow(Net::HTTP::Post).to receive(:new).and_return(double("request").tap { |req| allow(req).to receive(:[]=); allow(req).to receive(:body=) })
      end

      it "makes successful GraphQL request" do
        result = service.send(:make_graphql_request, "https://example.com/graphql", query, variables)

        expect(result).to eq({ "data" => { "test" => "result" } })
      end

      context "when API returns error" do
        let(:error_response) { double("response", code: "400", body: "GraphQL error") }
        let(:http_double_error) do
          double("http").tap do |http|
            allow(http).to receive(:use_ssl=)
            allow(http).to receive(:read_timeout=)
            allow(http).to receive(:open_timeout=)
            allow(http).to receive(:request).and_return(error_response)
          end
        end
        before do
          allow(Net::HTTP).to receive(:new).and_return(http_double_error)
          allow(Net::HTTP::Post).to receive(:new).and_return(double("request").tap { |req| allow(req).to receive(:[]=); allow(req).to receive(:body=) })
        end
        it "raises error" do
          expect {
            service.send(:make_graphql_request, "https://example.com/graphql", query, variables)
          }.to raise_error("GraphQL request failed: GraphQL API error: 400 - GraphQL error")
        end
      end
    end
  end
end
