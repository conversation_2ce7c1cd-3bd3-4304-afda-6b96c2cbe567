require "spec_helper"
require "rails_helper" if File.exist?(File.expand_path("../rails_helper.rb", __dir__))

RSpec.describe UserDeletionService do
  let(:user) { create_user }
  let(:firebase_auth_service) { instance_double(FirebaseAuthService) }
  let(:shopify_service) { instance_double(ShopifyService) }
  let(:service) { described_class.new(user, firebase_auth_service: firebase_auth_service, shopify_service: shopify_service) }

  describe "#can_delete?" do
    context "when user can be safely deleted" do
      before do
        allow(service).to receive(:has_pending_orders?).and_return(false)
        allow(service).to receive(:has_active_cart_items?).and_return(false)
      end

      it "returns can_delete true" do
        result = service.can_delete?
        expect(result[:can_delete]).to be true
      end
    end

    context "when user has pending orders" do
      before do
        allow(service).to receive(:has_pending_orders?).and_return(true)
        allow(service).to receive(:has_active_cart_items?).and_return(false)
      end

      it "returns can_delete false with error message" do
        result = service.can_delete?
        expect(result[:can_delete]).to be false
        expect(result[:errors]).to include("Cannot delete account with pending orders")
      end
    end

    context "when user has active cart items" do
      before do
        allow(service).to receive(:has_pending_orders?).and_return(false)
        allow(service).to receive(:has_active_cart_items?).and_return(true)
      end

      it "returns can_delete false with error message" do
        result = service.can_delete?
        expect(result[:can_delete]).to be false
        expect(result[:errors]).to include("Please empty your cart before deleting your account")
      end
    end
  end

  describe "#delete_account" do
    context "when deletion is successful" do
      before do
        allow(service).to receive(:can_delete?).and_return({ can_delete: true })
        allow(firebase_auth_service).to receive(:delete_user).with(user.firebase_uid)
        allow(shopify_service).to receive(:deactivate_customer).with(user.shopify_customer_id)
        allow(user).to receive(:destroy!)
        allow(user).to receive(:destroyed?).and_return(true)
      end

      it "deletes user from all systems" do
        result = service.delete_account

        expect(firebase_auth_service).to have_received(:delete_user).with(user.firebase_uid)
        expect(shopify_service).to have_received(:deactivate_customer).with(user.shopify_customer_id)
        expect(user).to have_received(:destroy!)
        
        expect(result[:firebase_deleted]).to be true
        expect(result[:shopify_deleted]).to be true
        expect(result[:user_deleted]).to be true
        expect(result[:errors]).to be_empty
      end
    end

    context "when user cannot be deleted" do
      before do
        allow(service).to receive(:can_delete?).and_return({ 
          can_delete: false, 
          errors: ["Cannot delete account with pending orders"] 
        })
      end

      it "raises validation error" do
        expect {
          service.delete_account
        }.to raise_error("Cannot delete account: Cannot delete account with pending orders")
      end
    end

    context "when Firebase deletion fails" do
      before do
        allow(service).to receive(:can_delete?).and_return({ can_delete: true })
        allow(firebase_auth_service).to receive(:delete_user).and_raise("Firebase error")
      end

      it "returns error result" do
        result = service.delete_account

        expect(result[:firebase_deleted]).to be false
        expect(result[:shopify_deleted]).to be false
        expect(result[:user_deleted]).to be false
        expect(result[:errors]).to include("Firebase error")
      end
    end

    context "when Firebase user not found" do
      before do
        allow(service).to receive(:can_delete?).and_return({ can_delete: true })
        allow(firebase_auth_service).to receive(:delete_user).and_raise("USER_NOT_FOUND")
        allow(shopify_service).to receive(:deactivate_customer).with(user.shopify_customer_id)
        allow(user).to receive(:destroy!)
        allow(user).to receive(:destroyed?).and_return(true)
      end

      it "continues with deletion process" do
        result = service.delete_account

        expect(result[:firebase_deleted]).to be true
        expect(result[:shopify_deleted]).to be true
        expect(result[:user_deleted]).to be true
      end
    end
  end

  private

  def create_user
    User.new(
      id: BSON::ObjectId.new,
      firebase_uid: "firebase_123",
      email: "<EMAIL>",
      name: "Test User",
      shopify_customer_id: "shopify_456"
    ).tap do |user|
      allow(user).to receive(:orders).and_return(double("orders", exists?: false))
      allow(user).to receive(:carts).and_return(double("carts", exists?: false))
    end
  end
end
