require "spec_helper"

RSpec.describe AuthService do
  let(:firebase_auth_service) { instance_double(FirebaseAuthService) }
  let(:shopify_service) { instance_double(ShopifyService) }
  let(:service) { AuthService.new(firebase_auth_service: firebase_auth_service, shopify_service: shopify_service) }
  let(:token) { "valid.firebase.token" }
  let(:firebase_user) { mock_firebase_user }
  let(:shopify_customer) { mock_shopify_customer }

  describe "#authenticate" do
    context "with valid token" do
      before do
        allow(firebase_auth_service).to receive(:verify_token).with(token).and_return(firebase_user)
      end

      context "when user exists in database" do
        let!(:existing_user) { create_user(firebase_uid: firebase_user["localId"], email: firebase_user["email"], name: firebase_user["displayName"]) }

        context "and has shopify_customer_id" do
          before do
            existing_user.update!(shopify_customer_id: shopify_customer["id"])
          end

          it "returns authentication data with existing user" do
            result = service.authenticate(token)

            expect(result).to include(:token, :user)
            expect(result[:user][:id]).to eq(existing_user.id.to_s)
            expect(result[:user][:email]).to eq(firebase_user["email"])
            expect(result[:user][:shopify_customer_id]).to eq(shopify_customer["id"])
          end

          it "generates a JWT token" do
            result = service.authenticate(token)

            expect(result[:token]).to be_present
            decoded_token = JWT.decode(result[:token], ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: "HS256" }).first
            expect(decoded_token["user_id"]).to eq(existing_user.id.to_s)
          end
        end

        context "and does not have shopify_customer_id" do
          before do
            existing_user.update!(shopify_customer_id: nil)
          end

          context "when customer exists in Shopify" do
            before do
              allow(shopify_service).to receive(:find_customer_by_email).with(firebase_user["email"]).and_return(shopify_customer)
            end

            it "links existing user to Shopify customer" do
              result = service.authenticate(token)

              expect(result[:user][:shopify_customer_id]).to eq(shopify_customer["id"])
              expect(existing_user.reload.shopify_customer_id).to eq(shopify_customer["id"])
            end
          end

          context "when customer does not exist in Shopify" do
            before do
              allow(shopify_service).to receive(:find_customer_by_email).with(firebase_user["email"]).and_return(nil)
              allow(shopify_service).to receive(:create_customer).with(
                email: firebase_user["email"],
                name: firebase_user["displayName"],
              ).and_return(shopify_customer)
            end

            it "creates new Shopify customer and links to user" do
              result = service.authenticate(token)

              expect(result[:user][:shopify_customer_id]).to eq(shopify_customer["id"])
              expect(existing_user.reload.shopify_customer_id).to eq(shopify_customer["id"])
            end
          end
        end
      end

      context "when user does not exist in database" do
        context "when customer exists in Shopify" do
          before do
            allow(shopify_service).to receive(:find_customer_by_email).with(firebase_user["email"]).and_return(shopify_customer)
          end

          it "creates new user linked to existing Shopify customer" do
            expect {
              result = service.authenticate(token)
            }.to change(User, :count).by(1)

            new_user = User.last
            expect(new_user.firebase_uid).to eq(firebase_user["localId"])
            expect(new_user.email).to eq(firebase_user["email"])
            expect(new_user.shopify_customer_id).to eq(shopify_customer["id"])
          end
        end

        context "when customer does not exist in Shopify" do
          before do
            allow(shopify_service).to receive(:find_customer_by_email).with(firebase_user["email"]).and_return(nil)
            allow(shopify_service).to receive(:create_customer).with(
              email: firebase_user["email"],
              name: firebase_user["displayName"],
            ).and_return(shopify_customer)
          end

          it "creates new user and new Shopify customer" do
            expect {
              result = service.authenticate(token)
            }.to change(User, :count).by(1)

            new_user = User.last
            expect(new_user.firebase_uid).to eq(firebase_user["localId"])
            expect(new_user.email).to eq(firebase_user["email"])
            expect(new_user.shopify_customer_id).to eq(shopify_customer["id"])
          end
        end
      end
    end

    context "with missing token" do
      it "raises error for nil token" do
        expect {
          service.authenticate(nil)
        }.to raise_error("Authorization token is missing")
      end

      it "raises error for blank token" do
        expect {
          service.authenticate("")
        }.to raise_error("Authorization token is missing")
      end

      it "raises error for whitespace token" do
        expect {
          service.authenticate("   ")
        }.to raise_error("Authorization token is missing")
      end
    end

    context "when Firebase token verification fails" do
      before do
        allow(firebase_auth_service).to receive(:verify_token).and_raise("Token verification failed")
      end

      it "raises the error from Firebase service" do
        expect {
          service.authenticate(token)
        }.to raise_error("Token verification failed")
      end
    end

    context "when user creation fails" do
      before do
        allow(firebase_auth_service).to receive(:verify_token).with(token).and_return(firebase_user)
        allow(shopify_service).to receive(:find_customer_by_email).with(firebase_user["email"]).and_return(nil)
        allow(shopify_service).to receive(:create_customer).with(
          email: firebase_user["email"],
          name: firebase_user["displayName"],
        ).and_return(shopify_customer)

        # Stub User.create to return a user that fails validation
        invalid_user = User.new(firebase_uid: firebase_user["localId"], email: firebase_user["email"], name: firebase_user["displayName"])
        allow(invalid_user).to receive(:persisted?).and_return(false)
        allow(invalid_user).to receive_message_chain(:errors, :full_messages).and_return(["Firebase uid has already been taken"])
        allow(User).to receive(:create).and_return(invalid_user)
      end

      it "raises error when user validation fails" do
        expect {
          service.authenticate(token)
        }.to raise_error(/Firebase uid/)
      end
    end

    context "when Shopify customer creation fails" do
      before do
        allow(firebase_auth_service).to receive(:verify_token).with(token).and_return(firebase_user)
        allow(shopify_service).to receive(:find_customer_by_email).with(firebase_user["email"]).and_return(nil)
        allow(shopify_service).to receive(:create_customer).and_raise("Shopify API error")
      end

      it "raises the Shopify service error" do
        expect {
          service.authenticate(token)
        }.to raise_error("Shopify API error")
      end
    end
  end

  describe "private methods" do
    describe "#generate_jwt_token" do
      let(:user) { create_user }

      it "generates a valid JWT token" do
        token = service.send(:generate_jwt_token, user)

        expect(token).to be_present
        decoded = JWT.decode(token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: "HS256" }).first
        expect(decoded["user_id"]).to eq(user.id.to_s)
        expect(decoded["exp"]).to be > Time.now.to_i
      end
    end

    describe "#user_data" do
      let(:user) { create_user }

      it "returns formatted user data" do
        data = service.send(:user_data, user)

        expect(data).to include(
          id: user.id.to_s,
          email: user.email,
          name: user.name,
          shopify_customer_id: user.shopify_customer_id,
          created_at: user.created_at,
          updated_at: user.updated_at,
        )
      end
    end
  end
end
