require "spec_helper"

RSpec.describe CartService do
  let(:user) { create_user }
  let(:service) { CartService.new(user) }
  let(:shopify_service) { instance_double(ShopifyService) }
  let(:valid_item_params) do
    {
      variant_id: Faker::Number.number(digits: 10).to_s,
      product_id: Faker::Number.number(digits: 10).to_s,
      quantity: 2,
      price: 29.99,
      title: "Test Product",
    }
  end

  before do
    allow(ShopifyService).to receive(:new).and_return(shopify_service)
  end

  describe "#find_or_create_cart" do
    context "when user has no active cart" do
      it "creates a new active cart" do
        expect {
          cart = service.find_or_create_cart
        }.to change(Cart, :count).by(1)

        cart = service.find_or_create_cart
        expect(cart.user).to eq(user)
        expect(cart.status).to eq("active")
      end
    end

    context "when user has an active cart" do
      let!(:existing_cart) { create_cart(user, status: "active") }

      it "returns the existing active cart" do
        cart = service.find_or_create_cart
        expect(cart).to eq(existing_cart)
      end

      it "does not create a new cart" do
        expect {
          service.find_or_create_cart
        }.not_to change(Cart, :count)
      end
    end

    context "when user has inactive carts" do
      let!(:inactive_cart) { create_cart(user, status: "checked_out") }

      it "creates a new active cart" do
        expect {
          cart = service.find_or_create_cart
        }.to change(Cart, :count).by(1)

        cart = service.find_or_create_cart
        expect(cart.status).to eq("active")
        expect(cart).not_to eq(inactive_cart)
      end
    end
  end




end
