require "spec_helper"

RSpec.describe FirebaseAuthService do
  let(:service) { FirebaseAuthService.new }
  let(:valid_token) { "valid.jwt.token" }
  let(:invalid_token) { "invalid.token" }
  let(:expired_token) { "expired.jwt.token" }

  # Mock Firebase configuration
  let(:mock_config) do
    {
      "project_id" => "travelgator",
      "private_key" => "-----BEGIN PRIVATE KEY-----\nMOCK_KEY\n-----END PRIVATE KEY-----",
      "client_email" => "<EMAIL>",
    }
  end

  # Mock public keys response
  let(:mock_public_keys) do
    {
      "kid1" => "-----BEGIN CERTIFICATE-----\nMOCK_CERT\n-----END CERTIFICATE-----",
    }
  end

  # Mock decoded token
  let(:mock_decoded_token) do
    {
      "sub" => "user123",
      "email" => "<EMAIL>",
      "name" => "Test User",
      "iss" => "https://securetoken.google.com/travelgator",
      "aud" => "travelgator",
      "exp" => 1.hour.from_now.to_i,
      "iat" => Time.now.to_i,
    }
  end

  before do
    # Mock file system operations
    allow(File).to receive(:exist?).and_return(true)
    allow(File).to receive(:read).and_return(mock_config.to_json)
    allow(File).to receive(:open).and_return(StringIO.new(mock_config.to_json))

    # Mock OpenSSL operations
    allow(OpenSSL::PKey::RSA).to receive(:new).and_return(double("private_key"))
    allow(OpenSSL::X509::Certificate).to receive(:new).and_return(double("cert", public_key: double("public_key")))

    # Mock Google Auth
    allow(Google::Auth::ServiceAccountCredentials).to receive(:make_creds).and_return(double("credentials"))
    identity_service_double = double("identity_service")
    allow(identity_service_double).to receive(:authorization=)
    allow(Google::Apis::IdentitytoolkitV3::IdentityToolkitService).to receive(:new).and_return(identity_service_double)

    # Mock Net::HTTP for fetch_firebase_public_keys
    http_double = double("http")
    allow(http_double).to receive(:use_ssl=)
    allow(http_double).to receive(:read_timeout=)
    allow(http_double).to receive(:open_timeout=)
    allow(http_double).to receive(:request).and_return(double("response", code: "200", body: mock_public_keys.to_json))
    allow(Net::HTTP).to receive(:new).and_return(http_double)
    allow(Net::HTTP::Get).to receive(:new).and_return(double("request"))
  end

  describe "#verify_token" do
    context "with valid token" do
      before do
        allow(service).to receive(:fetch_firebase_public_keys).and_return(mock_public_keys)
        allow(JWT).to receive(:decode).and_return([mock_decoded_token, { "kid" => "kid1" }])
      end

      it "returns formatted user data" do
        result = service.verify_token(valid_token)

        expect(result).to include(
          "localId" => "user123",
          "email" => "<EMAIL>",
          "displayName" => "Test User",
        )
      end

      it "handles token without name field" do
        token_without_name = mock_decoded_token.merge("name" => nil)
        allow(JWT).to receive(:decode).and_return([token_without_name, { "kid" => "kid1" }])

        result = service.verify_token(valid_token)

        expect(result["displayName"]).to eq("test")
      end

      it "handles token with user_id instead of sub" do
        token_with_user_id = mock_decoded_token.merge("sub" => nil, "user_id" => "user456")
        allow(JWT).to receive(:decode).and_return([token_with_user_id, { "kid" => "kid1" }])

        expect {
          service.verify_token(valid_token)
        }.to raise_error("Token verification failed: Token subject (sub) is missing")
      end
    end

    context "with missing token" do
      it "raises error for nil token" do
        expect {
          service.verify_token(nil)
        }.to raise_error("Token verification failed: Token is missing")
      end

      it "raises error for blank token" do
        expect {
          service.verify_token("")
        }.to raise_error("Token verification failed: Token is missing")
      end

      it "raises error for whitespace token" do
        expect {
          service.verify_token("   ")
        }.to raise_error("Token verification failed: Token is missing")
      end
    end

    context "with invalid token format" do
      before do
        allow(JWT).to receive(:decode).and_raise(JWT::DecodeError.new("Invalid token"))
      end

      it "raises error for invalid token format" do
        expect {
          service.verify_token(invalid_token)
        }.to raise_error("Token verification failed: Invalid token format: Invalid token")
      end
    end

    context "with expired token" do
      let(:expired_decoded_token) { mock_decoded_token.merge("exp" => 1.hour.ago.to_i) }

      before do
        allow(service).to receive(:fetch_firebase_public_keys).and_return(mock_public_keys)
        allow(JWT).to receive(:decode).and_return([expired_decoded_token, { "kid" => "kid1" }])
      end

      it "raises error for expired token" do
        expect {
          service.verify_token(expired_token)
        }.to raise_error("Token verification failed: Token has expired")
      end
    end

    context "with invalid token issuer" do
      let(:invalid_issuer_token) { mock_decoded_token.merge("iss" => "https://wrong.google.com/travelgator") }

      before do
        allow(service).to receive(:fetch_firebase_public_keys).and_return(mock_public_keys)
        allow(JWT).to receive(:decode).and_return([invalid_issuer_token, { "kid" => "kid1" }])
      end

      it "raises error for invalid issuer" do
        expect {
          service.verify_token(valid_token)
        }.to raise_error("Token verification failed: Invalid token issuer")
      end
    end

    context "with invalid token audience" do
      let(:invalid_audience_token) { mock_decoded_token.merge("aud" => "wrong_project") }

      before do
        allow(service).to receive(:fetch_firebase_public_keys).and_return(mock_public_keys)
        allow(JWT).to receive(:decode).and_return([invalid_audience_token, { "kid" => "kid1" }])
      end

      it "raises error for invalid audience" do
        expect {
          service.verify_token(valid_token)
        }.to raise_error("Token verification failed: Invalid token audience")
      end
    end

    context "with missing token subject" do
      let(:no_subject_token) { mock_decoded_token.except("sub") }

      before do
        allow(service).to receive(:fetch_firebase_public_keys).and_return(mock_public_keys)
        allow(JWT).to receive(:decode).and_return([no_subject_token, { "kid" => "kid1" }])
      end

      it "raises error for missing subject" do
        expect {
          service.verify_token(valid_token)
        }.to raise_error("Token verification failed: Token subject (sub) is missing")
      end
    end
  end

  describe "#get_user_by_uid" do
    let(:uid) { "user123" }
    let(:mock_user_info) do
      double("user_info",
             local_id: "user123",
             email: "<EMAIL>",
             display_name: "Test User",
             email_verified: true,
             disabled: false,
             created_at: "2023-01-01T00:00:00Z",
             last_login_at: "2023-01-02T00:00:00Z",
             provider_user_info: [])
    end

    before do
      allow(service.instance_variable_get(:@identity_service)).to receive(:get_account_info).and_return(
        double("response", users: [mock_user_info])
      )
    end

    it "returns formatted user info" do
      result = service.get_user_by_uid(uid)

      expect(result).to include(
        "localId" => "user123",
        "email" => "<EMAIL>",
        "displayName" => "Test User",
        "emailVerified" => true,
        "disabled" => false,
        "createdAt" => "2023-01-01T00:00:00Z",
        "lastLoginAt" => "2023-01-02T00:00:00Z",
        "providerUserInfo" => [],
      )
    end

    context "when user not found" do
      before do
        allow(service.instance_variable_get(:@identity_service)).to receive(:get_account_info).and_return(
          double("response", users: [])
        )
      end

      it "returns nil" do
        result = service.get_user_by_uid(uid)
        expect(result).to be_nil
      end
    end

    context "when API call fails" do
      before do
        allow(service.instance_variable_get(:@identity_service)).to receive(:get_account_info).and_raise(
          Google::Apis::Error.new("API Error")
        )
      end

      it "raises error" do
        expect {
          service.get_user_by_uid(uid)
        }.to raise_error("Failed to get user: API Error")
      end
    end
  end

  describe "#get_user_by_email" do
    let(:email) { "<EMAIL>" }
    let(:mock_user_info) do
      double("user_info",
             local_id: "user123",
             email: "<EMAIL>",
             display_name: "Test User",
             email_verified: true,
             disabled: false,
             created_at: "2023-01-01T00:00:00Z",
             last_login_at: "2023-01-02T00:00:00Z",
             provider_user_info: [])
    end

    before do
      allow(service.instance_variable_get(:@identity_service)).to receive(:get_account_info).and_return(
        double("response", users: [mock_user_info])
      )
    end

    it "returns formatted user info" do
      result = service.get_user_by_email(email)

      expect(result).to include(
        "localId" => "user123",
        "email" => "<EMAIL>",
        "displayName" => "Test User",
        "emailVerified" => true,
        "disabled" => false,
        "createdAt" => "2023-01-01T00:00:00Z",
        "lastLoginAt" => "2023-01-02T00:00:00Z",
        "providerUserInfo" => [],
      )
    end

    context "when user not found" do
      before do
        allow(service.instance_variable_get(:@identity_service)).to receive(:get_account_info).and_return(
          double("response", users: [])
        )
      end

      it "returns nil" do
        result = service.get_user_by_email(email)
        expect(result).to be_nil
      end
    end
  end

  describe "private methods" do
    describe "#fetch_firebase_public_keys" do
      let(:mock_response) { double("response", code: "200", body: mock_public_keys.to_json) }

      before do
        # Override the global mocks for this specific test
        http_double = double("http")
        allow(http_double).to receive(:use_ssl=)
        allow(http_double).to receive(:read_timeout=)
        allow(http_double).to receive(:open_timeout=)
        allow(http_double).to receive(:request).and_return(mock_response)
        allow(Net::HTTP).to receive(:new).and_return(http_double)
        allow(Net::HTTP::Get).to receive(:new).and_return(double("request"))
      end

      it "fetches public keys successfully" do
        result = service.send(:fetch_firebase_public_keys)

        expect(result).to eq(mock_public_keys)
      end

      context "when API returns error" do
        let(:error_response) { double("response", code: "500", body: "Server Error") }

        before do
          http_double = double("http")
          allow(http_double).to receive(:use_ssl=)
          allow(http_double).to receive(:read_timeout=)
          allow(http_double).to receive(:open_timeout=)
          allow(http_double).to receive(:request).and_return(error_response)
          allow(Net::HTTP).to receive(:new).and_return(http_double)
          allow(Net::HTTP::Get).to receive(:new).and_return(double("request"))
        end

        it "raises error" do
          expect {
            service.send(:fetch_firebase_public_keys)
          }.to raise_error(/Failed to fetch verification keys/)
        end
      end

      context "when response is invalid JSON" do
        let(:invalid_response) { double("response", code: "200", body: "invalid json") }

        before do
          http_double = double("http")
          allow(http_double).to receive(:use_ssl=)
          allow(http_double).to receive(:read_timeout=)
          allow(http_double).to receive(:open_timeout=)
          allow(http_double).to receive(:request).and_return(invalid_response)
          allow(Net::HTTP).to receive(:new).and_return(http_double)
          allow(Net::HTTP::Get).to receive(:new).and_return(double("request"))
        end

        it "raises error" do
          expect {
            service.send(:fetch_firebase_public_keys)
          }.to raise_error(/Invalid public keys response/)
        end
      end

      context "when request times out" do
        before do
          http_double = double("http")
          allow(http_double).to receive(:use_ssl=)
          allow(http_double).to receive(:read_timeout=)
          allow(http_double).to receive(:open_timeout=)
          allow(http_double).to receive(:request).and_raise(Timeout::Error.new("Timeout"))
          allow(Net::HTTP).to receive(:new).and_return(http_double)
          allow(Net::HTTP::Get).to receive(:new).and_return(double("request"))
        end

        it "raises error" do
          expect {
            service.send(:fetch_firebase_public_keys)
          }.to raise_error(/Timeout fetching verification keys/)
        end
      end
    end

    describe "#validate_token_claims" do
      it "validates all token claims successfully" do
        expect {
          service.send(:validate_token_claims, mock_decoded_token)
        }.not_to raise_error
      end

      it "validates token expiration" do
        expired_token = mock_decoded_token.merge("exp" => 1.hour.ago.to_i)

        expect {
          service.send(:validate_token_claims, expired_token)
        }.to raise_error("Token has expired")
      end

      it "validates token issuer" do
        invalid_issuer = mock_decoded_token.merge("iss" => "https://wrong.google.com/travelgator")

        expect {
          service.send(:validate_token_claims, invalid_issuer)
        }.to raise_error("Invalid token issuer")
      end

      it "validates token audience" do
        invalid_audience = mock_decoded_token.merge("aud" => "wrong_project")

        expect {
          service.send(:validate_token_claims, invalid_audience)
        }.to raise_error("Invalid token audience")
      end

      it "validates token subject" do
        no_subject = mock_decoded_token.except("sub")

        expect {
          service.send(:validate_token_claims, no_subject)
        }.to raise_error("Token subject (sub) is missing")
      end
    end
  end

  describe "initialization" do
    context "when service account file is missing" do
      before do
        allow(File).to receive(:exist?).and_return(false)
      end

      it "raises error" do
        expect {
          FirebaseAuthService.new
        }.to raise_error(/Firebase service account file not found/)
      end
    end

    context "when service account file is invalid JSON" do
      before do
        allow(File).to receive(:read).and_return("invalid json")
      end

      it "raises error" do
        expect {
          FirebaseAuthService.new
        }.to raise_error(JSON::ParserError)
      end
    end
  end
end
