class RedisCacheSubscriber
  CHANNEL = "cache:clear"

  def self.start
    Thread.new do
      begin
        redis_url = ENV['REDIS_URL'] || 'redis://localhost:6380/0'
        redis_password = ENV['REDIS_PASSWORD']

        Rails.logger.info "[RedisCacheSubscriber] Attempting to connect to Redis at: #{redis_url}"

        # Only pass password if it's set
        if redis_password.present?
          redis = Redis.new(url: redis_url, password: redis_password)
        else
          redis = Redis.new(url: redis_url)
        end

        # Test the connection
        redis.ping
        Rails.logger.info "[RedisCacheSubscriber] Successfully connected to Redis"

        redis.subscribe(CHANNEL) do |on|
          on.message do |_channel, message|
            Rails.logger.info "[RedisCacheSubscriber] Received: #{message}"
            Rails.cache.clear
          end
        end
      rescue StandardError => e
        Rails.logger.error "[RedisCacheSubscriber] Failed to connect to Redis: #{e.message}"
        Rails.logger.error "[RedisCacheSubscriber] Redis cache subscriber disabled"
        # Don't crash the application if Red<PERSON> is not available
      end
    end
  end
end

# Only start if Red<PERSON> is available and <PERSON><PERSON> is defined
if defined?(Puma) && ENV['REDIS_URL'].present?
  RedisCacheSubscriber.start
else
  Rails.logger.info "[RedisCacheSubscriber] Skipping Redis cache subscriber (Redis not configured or Puma not available)"
end
