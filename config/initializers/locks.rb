# class RedisConfiguration
#   cattr_accessor :redis_url, :redis_password, :redis_port, :redis_timeout, :redis_namespace

#   def self.configure(redis_url: 'redis://localhost', redis_password: nil, redis_port: '6379', redis_timeout: 10, redis_namespace: nil)
#     self.redis_url = redis_url
#     self.redis_password = redis_password
#     self.redis_port = redis_port
#     self.redis_timeout = redis_timeout
#     self.redis_namespace = redis_namespace
#   end
# end

# RedisConfiguration.configure(
#   redis_url: Rails.application.redis_url,
#   redis_namespace: Rails.application.redis_namespace,
#   redis_password: Rails.application.redis_password,
# )
