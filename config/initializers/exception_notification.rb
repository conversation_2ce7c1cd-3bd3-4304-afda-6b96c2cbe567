require 'exception_notification/rails'

require 'exception_notification/sidekiq'


# if Rails.env.production?
#   ExceptionNotification.configure do |config|
#     # Ignore additional exception types.
#     # ActiveRecord::RecordNotFound, Mongoid::Errors::DocumentNotFound, AbstractController::ActionNotFound and ActionController::RoutingError are already added.
#     # config.ignored_exceptions += %w{ActionView::TemplateError CustomError}

#     # Adds a condition to decide when an exception must be ignored or not.
#     # The ignore_if method can be invoked multiple times to add extra conditions.
#     # config.ignore_if do |exception, options|
#     #   not Rails.env.production?
#     # end

#     # Notifiers =================================================================

#     # Email notifier sends notifications by email.
#     config.add_notifier :email, {
#       email_prefix: "[#{Rails.application.application_namespace}] ",
#       sender_address: EmailManager.singleton.sender_email,
#       exception_recipients: Rails.application.developer_emails,
#     }

#     # Campfire notifier sends notifications to your Campfire room. Requires 'tinder' gem.
#     # config.add_notifier :campfire, {
#     #   :subdomain => 'my_subdomain',
#     #   :token => 'my_token',
#     #   :room_name => 'my_room'
#     # }

#     # HipChat notifier sends notifications to your HipChat room. Requires 'hipchat' gem.
#     # config.add_notifier :hipchat, {
#     #   :api_token => 'my_token',
#     #   :room_name => 'my_room'
#     # }

#     # Webhook notifier sends notifications over HTTP protocol. Requires 'httparty' gem.
#     # config.add_notifier :webhook, {
#     #   :url => 'http://example.com:5555/hubot/path',
#     #   :http_method => :post
#     # }
#   end

#   Rails.application.config.middleware.use ExceptionNotification::Rack,
#     email:  {
#       deliver_with: :deliver,
#       email_prefix: "[#{Rails.application.application_namespace}] ",
#       sender_address: EmailManager.singleton.sender_email,
#       exception_recipients: Rails.application.developer_emails,
#     },
#     error_grouping: true,
#     error_grouping_period: 5.minutes
# end
