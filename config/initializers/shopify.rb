# Shopify API Configuration
class ShopifyGraph<PERSON><PERSON>lient
  def initialize
    # Check if Shopify credentials are available
    unless <PERSON>N<PERSON>['SHOPIFY_SHOP_DOMAIN'].present? && ENV['SHOPIFY_ACCESS_TOKEN'].present?
      raise StandardError, "Shopify credentials not configured. Please set SHOPIFY_SHOP_DOMAIN and SHOPIFY_ACCESS_TOKEN environment variables."
    end

    @session = ShopifyAPI::Auth::Session.new(
      shop: ENV['SHOPIFY_SHOP_DOMAIN'],
      access_token: ENV['SHOPIFY_ACCESS_TOKEN']
    )
    @client = ShopifyAPI::Clients::Graphql::Admin.new(session: @session)
  end

  def query(query_string, variables = {})
    @client.query(query: query_string, variables: variables)
  rescue StandardError => e
    Rails.logger.error "Shopify GraphQL Error: #{e.message}"
    Rails.logger.error "Query: #{query_string}"
    Rails.logger.error "Variables: #{variables}"
    raise e
  end
end

# Initialize Shopify API context only if credentials are available
# For GraphQL operations, we only need shop domain and access token
if ENV['SHOPIFY_SHOP_DOMAIN'].present? && ENV['SHOPIFY_ACCESS_TOKEN'].present?
  # Set up minimal context for GraphQL operations
  ShopifyAPI::Context.setup(
    api_key: 'dummy_key',  # Required by the gem but not used for GraphQL with access tokens
    api_secret_key: 'dummy_secret',  # Required by the gem but not used for GraphQL with access tokens
    host: ENV['SHOPIFY_APP_HOST'] || 'localhost:3000',
    scope: 'read_orders,write_orders',
    api_version: '2023-04',
    is_embedded: false,
    is_private: true
  )

  Rails.logger.info "Shopify API initialized with shop: #{ENV['SHOPIFY_SHOP_DOMAIN']}"
else
  Rails.logger.warn "Shopify API credentials not found - Shopify features will be disabled"
end
