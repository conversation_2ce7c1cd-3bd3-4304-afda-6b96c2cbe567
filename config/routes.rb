Rails.application.routes.draw do
  namespace :api do
    post "auth/authenticate", to: "auth#authenticate"
    post "auth/verify", to: "auth#verify"

    # User management routes
    get "users/profile", to: "users#profile"
    get "users/deletion_check", to: "users#deletion_check"
    delete "users/account", to: "users#destroy_account"

    # Remove products routes - Flutter calls Shopify directly

    resource :cart, only: [:show, :update]

    resources :orders, only: [:index, :show , :create] do
      collection do
        get :status       # GET /api/orders/status?order_id=xxx
      end
    end

    get "stripe/config", to: "stripe#config"
  end

  # Webhooks removed - using polling instead of webhooks
end
