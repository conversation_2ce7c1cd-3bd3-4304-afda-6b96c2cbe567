class Order
  include Documentable

  field :user_id, type: BSON::ObjectId
  field :shopify_order_id, type: String
  field :shopify_cart_id, type: String # Add cart ID for better tracking
  field :shopify_checkout_url, type: String
  field :payment_status, type: String
  field :payment_method, type: String
  field :stripe_payment_intent_id, type: String
  field :stripe_client_secret, type: String
  field :stripe_checkout_url, type: String
  field :stripe_status, type: String
  field :stripe_status_synced_at, type: Time
  field :order_type, type: String
  field :total_price, type: Float
  field :currency, type: String
  field :status, type: String
  field :items, type: Array
  field :original_total, type: Float
  # Cart tracking
  field :cart_id, type: String # Local cart ID for tracking

  # Order completion tracking
  field :completed_at, type: Time
  field :shopify_order_name, type: String
  field :final_total_price, type: Float

  belongs_to :user

  validates :user_id, presence: true
  validate :validate_items_structure
  validate :stripe_fields_present

  # Add index for better webhook lookup performance
  index({ shopify_cart_id: 1 })
  index({ user_id: 1, status: 1, created_at: -1 })

  # Check if payment is successful (paid or authorized)
  def stripe?
    payment_method.to_s.downcase == "stripe"
  end

  def payment_successful?
    ["paid", "authorized", "succeeded"].include?(payment_status&.downcase)
  end

  # Check if order is fully completed
  def fully_completed?
    status == "completed" && payment_successful?
  end

  def apply_stripe_status!(stripe_response)
    status_value = stripe_response[:status]

    attributes = {
      stripe_status: status_value,
      stripe_status_synced_at: Time.current,
      payment_status: map_stripe_status(status_value)
    }

    if status_value == "succeeded"
      attributes[:status] = "completed"
      attributes[:completed_at] = Time.current
      attributes[:final_total_price] = stripe_response[:amount_received].to_f if stripe_response[:amount_received]
    end

    update!(attributes.compact)

    clear_checked_out_items_from_cart if status_value == "succeeded"
  end

  # Complete order with status result from Shopify
  def complete_order(status_result)
    update!(
      status: "completed",
      payment_status: status_result[:payment_status],
      completed_at: Time.current,
      shopify_order_name: status_result[:order_name],
      final_total_price: status_result[:total_price]
    )

    # Clear checked-out items from cart after successful payment
    clear_checked_out_items_from_cart

    Rails.logger.info "Completed order #{id} with payment status: #{status_result[:payment_status]}"
  rescue StandardError => e
    Rails.logger.error "Failed to complete order #{id}: #{e.message}"
    raise e
  end
  scope :recent, -> { order(created_at: :desc) }
  scope :completed, -> { where(status: "completed") }
  scope :pending, -> { where(status: "pending") }

  private

  # Clear checked-out items from cart after successful payment
  def clear_checked_out_items_from_cart
    return unless cart_id.present?

    cart = Cart.find(cart_id)

    cart_items_before = cart.items.length
    Rails.logger.info "Clearing checked-out items from cart #{cart_id}: #{items.length} order line items"

    items.each do |order_item|
      variant_id = order_item["variant_id"]
      next if variant_id.blank?

      cart_item = cart.items.find { |item| item["variant_id"] == variant_id }
      next unless cart_item

      remaining_quantity = cart_item["quantity"].to_i - order_item["quantity"].to_i

      if remaining_quantity <= 0
        Rails.logger.info "Removing variant #{variant_id} from cart #{cart_id} (ordered quantity #{order_item["quantity"]})"
        cart.items.reject! { |item| item["variant_id"] == variant_id }
      else
        Rails.logger.info "Reducing quantity for variant #{variant_id} in cart #{cart_id}: #{cart_item["quantity"]} -> #{remaining_quantity}"
        cart_item["quantity"] = remaining_quantity
      end
    end

    cart.calculate_total
    cart.save!

    Rails.logger.info "Cart #{cart_id} had #{cart_items_before} items before cleanup, now #{cart.items.length}"
  rescue Mongoid::Errors::DocumentNotFound
    Rails.logger.warn "Cart #{cart_id} not found for completed order #{id}"
  rescue StandardError => e
    Rails.logger.error "Failed to clear checked-out items from cart #{cart_id}: #{e.message}"
    # Don"t raise error here as order was already completed successfully
    # This is a cleanup operation that shouldn"t fail the order completion
  end

  # Validates the structure and required fields of order items
  def validate_items_structure
    return if items.blank?

    items.each_with_index do |item, index|
      validate_item_required_fields(item, index)
      validate_item_data_types(item, index)
    end
  end

  def stripe_fields_present
    return unless stripe?

    if stripe_payment_intent_id.blank?
      errors.add(:stripe_payment_intent_id, "must be present for Stripe orders")
    end
  end

  def map_stripe_status(status_value)
    return if status_value.blank?

    case status_value
    when "succeeded"
      "succeeded"
    when "processing", "requires_capture", "requires_action"
      "processing"
    when "requires_payment_method", "requires_confirmation"
      "pending"
    when "canceled"
      "canceled"
    else
      status_value
    end
  end

  # Validates that each item has required fields
  def validate_item_required_fields(item, index)
    required_fields = %w[variant_id product_id quantity price title]
    missing_fields = required_fields.select { |field| item[field].blank? }

    if missing_fields.any?
      errors.add(:items, "Item #{index + 1} missing required fields: #{missing_fields.join(", ")}")
    end
  end

  # Validates data types and values for item fields
  def validate_item_data_types(item, index)
    if item["quantity"].present? && item["quantity"].to_i <= 0
      errors.add(:items, "Item #{index + 1} must have quantity greater than 0")
    end

    if item["price"].present? && item["price"].to_f < 0
      errors.add(:items, "Item #{index + 1} cannot have negative price")
    end
  end
end
