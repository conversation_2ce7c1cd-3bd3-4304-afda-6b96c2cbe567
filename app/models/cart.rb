class Cart
  include Documentable



  field :user_id, type: BSON::ObjectId
  field :items, type: Array, default: []
  field :total_price, type: Float, default: 0.0
  field :currency, type: String, default: "USD"
  field :status, type: String, default: "active"


  belongs_to :user
  # Indexes for performance optimization
  index({ user_id: 1, status: 1 })           # Find active user carts
  index({ user_id: 1, updated_at: -1 })      # Recent user carts
  index({ status: 1, updated_at: -1 })       # Cleanup old carts
  validates :user_id, presence: true

  # Client-trusted full cart replacement - simple and fast
  # Completely replaces cart contents with whatever client sends
  #
  # @param items_data [Array<Hash>] Complete cart items from client
  # @return [Cart] Returns self for method chaining
  def replace_items!(items_data)
    # Trust client completely - just replace everything
    self.items = items_data.select { |item| item[:quantity].to_i > 0 }
                          .map do |item|
                            {
                              "variant_id" => item[:variant_id].to_s,
                              "product_id" => item[:product_id].to_s,
                              "quantity" => item[:quantity].to_i,
                              "price" => item[:price].to_f,
                              "title" => item[:title].to_s,
                              "image_url" => item[:image_url].to_s,
                              "selected" => item.fetch(:selected, true)
                            }
                          end

    calculate_total
    save!
    self
  end





  def remove_items_by_variant_ids(variant_ids)
    return self if variant_ids.blank?

    variant_ids.each { |variant_id| validate_variant_id!(variant_id) }
    items.reject! { |item| variant_ids.include?(item["variant_id"]) }
    calculate_total
    save! if persisted?
    self
  end

  def calculate_total
    self.total_price = items.sum { |item| item["price"] * item["quantity"] }.round(2)
  end

  # Clear all items from cart
  #
  # @return [Cart] Returns self for method chaining
  def clear_items!
    self.items = []
    self.total_price = 0.0

    save!
    Rails.logger.info "Cart #{id} cleared - all items removed"
    self
  end



  # Calculate subtotal for all items
  def calculate_subtotal
    items.sum { |item| item["price"].to_f * item["quantity"].to_i }.round(2)
  end

  # Calculate total for specific selected items by variant IDs
  #
  # @param selected_variant_ids [Array<String>] Array of variant IDs to calculate total for
  # @return [Float] Total price for selected items, rounded to 2 decimal places
  def calculate_selected_items_total(selected_variant_ids)
    return 0.0 if selected_variant_ids.blank?

    total = 0.0
    items.each do |item|
      if selected_variant_ids.include?(item["variant_id"])
        item_total = item["price"].to_f * item["quantity"].to_i
        total += item_total
        Rails.logger.debug "Selected item #{item['variant_id']}: #{item['price']} x #{item['quantity']} = #{item_total}"
      end
    end

    Rails.logger.info "Selected items total: #{total} (from #{selected_variant_ids.length} selected items)"
    total.round(2)
  end

  # Get only selected items
  def selected_items
    items.select { |item| item["selected"] != false }
  end





  # Count of items (total quantity)
  def items_count
    items.sum { |item| item["quantity"].to_i }
  end

  # Validate cart for checkout - ensures cart has valid items
  def validate_for_checkout!
    raise "Cart is empty" if items.empty?

    selected_items = items.select { |item| item["selected"] != false }
    raise "No items selected for checkout" if selected_items.empty?

    validation_errors = validate_items
    raise "Cart validation failed: #{validation_errors.join(', ')}" if validation_errors.any?

    true
  end

  # =============================================================================
  # DATA FORMATTING METHODS (moved from CartsControllerService)
  # =============================================================================

  def to_enhanced_hash
    {
      id: id.to_s,
      user_id: user_id.to_s,
      items: format_items_for_api,
      items_count: items_count,
      subtotal: calculate_subtotal.round(2),
      currency: currency || "USD",
      total_price: total_price.round(2),

      created_at: created_at&.iso8601,
      updated_at: updated_at&.iso8601
    }
  end



  def format_items_for_api
    items.map do |item|
      {
        variant_id: item["variant_id"],
        product_id: item["product_id"],
        quantity: item["quantity"],
        price: item["price"].to_f.round(2),
        title: item["title"],
        image_url: item["image_url"],
        selected: item["selected"] != false,  # Default to true if not explicitly false
        total_price: (item["price"].to_f * item["quantity"]).round(2)
      }
    end
  end

  private

  # =============================================================================
  # VALIDATION METHODS (consolidated from services)
  # =============================================================================

  def validate_variant_id!(variant_id)
    raise "Variant ID is required" if variant_id.blank?
  end

  def validate_quantity!(quantity)
    raise "Quantity must be greater than 0" if quantity.to_i <= 0
  end

  def validate_items
    errors = []

    items.each_with_index do |item, index|
      errors << "Item #{index + 1}: Missing variant_id" unless item["variant_id"].present?
      errors << "Item #{index + 1}: Missing price" unless item["price"].present?
      errors << "Item #{index + 1}: Invalid quantity" unless item["quantity"].present? && item["quantity"] > 0
    end

    errors
  end
end
