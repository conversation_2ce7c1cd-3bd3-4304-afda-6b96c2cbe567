# Database-driven configuration system for TravelGator application settings
# Provides centralized configuration management with caching and singleton pattern
class AppSetting
  include Documentable

  # Core configuration fields - only settings actually used in the codebase
  field :enable_shopify, type: Boolean, default: true
  field :enable_stripe, type: <PERSON>olean, default: false
  field :jwt_expiration_hours, type: Integer, default: 24
  field :api_timeout_seconds, type: Integer, default: 30
  field :cache_expiration_minutes, type: Integer, default: 60

  # Singleton pattern - only one settings record should exist
  field :singleton_guard, type: Integer, default: 1

  # Indexes for performance
  index({ singleton_guard: 1 }, { unique: true })
  index({ updated_at: -1 })

  # Validations for data integrity
  validates :enable_shopify, inclusion: { in: [true, false] }
  validates :enable_stripe, inclusion: { in: [true, false] }
  validates :jwt_expiration_hours, presence: true, numericality: { greater_than: 0, less_than_or_equal_to: 168 } # Max 1 week
  validates :api_timeout_seconds, presence: true, numericality: { greater_than: 0, less_than_or_equal_to: 300 } # Max 5 minutes
  validates :cache_expiration_minutes, presence: true, numericality: { greater_than: 0, less_than_or_equal_to: 1440 } # Max 24 hours
  validates :singleton_guard, presence: true, uniqueness: true

  # Cache key for settings
  CACHE_KEY = 'app_settings_cached'.freeze
  CACHE_EXPIRATION = 60.minutes

  # Singleton pattern implementation with caching
  class << self
    # Get cached settings with 60-minute expiration
    def cached_settings
      Rails.cache.fetch(CACHE_KEY, expires_in: CACHE_EXPIRATION) do
        settings_record = first_or_create_singleton
        Rails.logger.info "AppSetting: Loading settings from database"
        settings_record
      end
    end

    # Reload settings and clear cache
    def reload!
      Rails.cache.delete(CACHE_KEY)
      clear_related_caches
      Rails.logger.info "AppSetting: Cache cleared and settings reloaded"
      cached_settings
    end

    # Update settings and invalidate cache
    def update_settings!(attributes)
      settings = first_or_create_singleton
      
      if settings.update(attributes)
        reload!
        Rails.logger.info "AppSetting: Settings updated successfully"
        settings
      else
        Rails.logger.error "AppSetting: Failed to update settings - #{settings.errors.full_messages.join(', ')}"
        raise StandardError, "Failed to update settings: #{settings.errors.full_messages.join(', ')}"
      end
    end

    # Convenience helper methods for easy access to actually used settings
    def shopify_enabled?
      cached_settings.enable_shopify
    end

    def stripe_enabled?
      cached_settings.enable_stripe
    end

    def enabled_payment_methods
      settings = cached_settings
      methods = []
      methods << 'shopify' if settings.enable_shopify
      methods << 'stripe' if settings.enable_stripe
      methods.empty? ? ['shopify'] : methods # Fallback to shopify if none enabled
    end

    def jwt_expiration
      cached_settings.jwt_expiration_hours.hours
    end

    def api_timeout
      cached_settings.api_timeout_seconds
    end

    def cache_expiration
      cached_settings.cache_expiration_minutes.minutes
    end

    private

    # Get or create the singleton settings record
    def first_or_create_singleton
      first || create_singleton_with_defaults
    end

    # Create singleton with fail-safe defaults for actually used settings
    def create_singleton_with_defaults
      create!(
        enable_shopify: true,
        enable_stripe: false,
        jwt_expiration_hours: 24,
        api_timeout_seconds: 30,
        cache_expiration_minutes: 60,
        singleton_guard: 1
      )
    rescue Mongoid::Errors::Validations => e
      Rails.logger.error "AppSetting: Failed to create singleton - #{e.message}"
      raise StandardError, "Failed to create default settings: #{e.message}"
    end

    # Clear related caches when settings change
    def clear_related_caches
      Rails.cache.delete_matched("collection_*")
      Rails.logger.debug "AppSetting: Cleared related collection caches"
    end
  end

end
