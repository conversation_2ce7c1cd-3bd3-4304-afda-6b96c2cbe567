class User
  include Documentable

  field :firebase_uid, type: String
  field :email, type: String
  field :name, type: String
  field :shopify_customer_id, type: String



  index({ firebase_uid: 1 }, { unique: true })
  index({ email: 1 })
  index({ email: 1, shopify_customer_id: 1 })  # Auth with Shopify lookup
  index({ created_at: -1 })                     # Recent users for analytics

  has_many :carts, dependent: :destroy
  has_many :orders, dependent: :destroy

  validates :firebase_uid, presence: true, uniqueness: true
  validates :email, presence: true
end
