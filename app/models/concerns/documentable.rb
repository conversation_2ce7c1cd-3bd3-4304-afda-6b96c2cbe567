module Documentable
  extend ActiveSupport::Concern

  included do
    include Mongoid::Document
    include Mongoid::Timestamps

    # Error handling methods for models
    def add_error(field, message)
      errors.add(field, message)
    end

    def with_error(field, message)
      add_error(field, message)
      self
    end

    # Validation helper methods

    # Common validation patterns
    def validate_presence_of(field, message = nil)
      value = send(field)
      if value.blank?
        add_error(field, message || "#{field.to_s.humanize} is required")
        return false
      end
      true
    end

    def validate_email_format(email_field = :email)
      email = send(email_field)
      return true if email.blank? # Let presence validation handle blank emails
      
      unless email.match?(/\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i)
        add_error(email_field, "Invalid email format")
        return false
      end
      true
    end

    def validate_positive_number(field, message = nil)
      value = send(field)
      return true if value.blank? # Let presence validation handle blank values
      
      if value.to_f <= 0
        add_error(field, message || "#{field.to_s.humanize} must be greater than 0")
        return false
      end
      true
    end

    def validate_non_negative_number(field, message = nil)
      value = send(field)
      return true if value.blank? # Let presence validation handle blank values
      
      if value.to_f < 0
        add_error(field, message || "#{field.to_s.humanize} cannot be negative")
        return false
      end
      true
    end

    def validate_inclusion_in(field, allowed_values, message = nil)
      value = send(field)
      return true if value.blank? # Let presence validation handle blank values
      
      unless allowed_values.include?(value)
        add_error(field, message || "#{field.to_s.humanize} must be one of: #{allowed_values.join(', ')}")
        return false
      end
      true
    end

    def validate_length(field, options = {})
      value = send(field)
      return true if value.blank? # Let presence validation handle blank values
      
      min = options[:minimum] || options[:min]
      max = options[:maximum] || options[:max]
      
      if min && value.length < min
        add_error(field, "#{field.to_s.humanize} must be at least #{min} characters")
        return false
      end
      
      if max && value.length > max
        add_error(field, "#{field.to_s.humanize} must be at most #{max} characters")
        return false
      end
      
      true
    end

    # Date validation helpers
    def validate_future_date(field, message = nil)
      date = send(field)
      return true if date.blank? # Let presence validation handle blank dates
      
      if date <= Time.current
        add_error(field, message || "#{field.to_s.humanize} must be in the future")
        return false
      end
      true
    end

    def validate_past_date(field, message = nil)
      date = send(field)
      return true if date.blank? # Let presence validation handle blank dates
      
      if date >= Time.current
        add_error(field, message || "#{field.to_s.humanize} must be in the past")
        return false
      end
      true
    end

    # Business logic validation helpers
    def validate_uniqueness_of(field, scope = nil, message = nil)
      value = send(field)
      return true if value.blank? # Let presence validation handle blank values
      
      query = { field => value }
      query.merge!(scope) if scope.is_a?(Hash)
      
      existing = self.class.where(query).where(:id.ne => id).first
      if existing
        add_error(field, message || "#{field.to_s.humanize} has already been taken")
        return false
      end
      true
    end

    # Conditional validation
    def validate_if(condition, &block)
      return true unless condition
      
      block.call
    end

    def validate_unless(condition, &block)
      return true if condition
      
      block.call
    end
  end

  class_methods do
    # Class-level validation helpers
    def validates_email_format_of(*fields)
      fields.each do |field|
        validate do
          validate_email_format(field)
        end
      end
    end

    def validates_positive_number(*fields)
      fields.each do |field|
        validate do
          validate_positive_number(field)
        end
      end
    end

    def validates_inclusion_in_list(field, allowed_values)
      validate do
        validate_inclusion_in(field, allowed_values)
      end
    end
  end
end
