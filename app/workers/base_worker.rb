class BaseWorker
  include Sidekiq::Worker

  def self.queue(instance, method)
    self.perform_async(instance.id.to_s, method)
  end

  def perform(instance_id, method)
    Locks::RedisLock.wait_lock("process-#{instance_id}", wait: self.should_wait) do
      started_at = Time.now

      instance = self.instance_class.where(id: instance_id).first

      if instance
        if instance.respond_to?(method.to_sym)
          instance.send(method.to_sym)
        end
      end

      finished_at = Time.now
      elapsed = (finished_at - started_at)

      if elapsed > self.alarm_threshold
        # sidekiq job took more than threshold
        Rails.logger.error("SIDEKIQ JOB - #{instance_id} #{method} [#{elapsed}]")
        # DevMessage.track("#{Rails.application.hostname} #{instance_id} #{method} [#{elapsed}]", 'SIDEKIQ JOB', important: true)
      end
    end
  end

  def instance_class
    raise "Sidekiq Worker - #{self.class.name} - instance_class not defined"
  end

  def alarm_threshold
    10
  end

  def should_wait
    false
  end
end
