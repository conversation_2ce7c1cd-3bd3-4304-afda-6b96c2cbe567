module ErrorLogging
  extend ActiveSupport::Concern

  private

  def log_and_raise(error, custom_error_class, message = nil)
    Rails.logger.error "#{self.class.name} error: #{error.class.name}"

    # Handle different types of error objects
    if error.respond_to?(:message)
      Rails.logger.error "Original error message: #{error.message}"
    else
      Rails.logger.error "Original error: #{error.inspect}"
    end

    # Only log backtrace if the error object has one
    if error.respond_to?(:backtrace) && error.backtrace
      Rails.logger.error "Backtrace: #{error.backtrace.first(5).join("\n")}"
    end

    final_message = message || (error.respond_to?(:message) ? error.message : error.to_s)
    raise custom_error_class, final_message
  end
end
