module AuthenticationExceptions
  class AuthenticationError < StandardError; end

  class MissingTokenError < AuthenticationError
    def message
      "Authorization token required"
    end
  end

  class InvalidTokenError < AuthenticationError
    def message
      "Invalid or expired token"
    end
  end

  class ShopifyIntegrationError < AuthenticationError
    def message
      "Failed to create user. Shopify integration error."
    end
  end

  class FirebaseConfigError < AuthenticationError
    def message
      "Firebase configuration error"
    end
  end

  class FirebaseTokenVerificationError < AuthenticationError
    def message
      "Failed to verify Firebase token"
    end
  end
end
