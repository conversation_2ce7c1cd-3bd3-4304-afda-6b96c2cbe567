module Api
  class BaseController < ApplicationController
    # Note: CSRF protection not needed for API controllers
    # API authentication is handled via tokens in headers

    protected

    # Standardized success response format
    def render_success(data, status: :ok)
      render json: {
        success: true,
        data: data
      }, status: status
    end

    # Standardized error response format
    def render_error(message, status: :unprocessable_entity)
      render json: {
        success: false,
        error: message
      }, status: status
    end

    # Legacy methods for backward compatibility
    def invalid_action!(message)
      render_error(message)
    end

    def invalid_resource!(resource)
      render_error(resource.errors.full_messages.to_sentence)
    end
  end
end
