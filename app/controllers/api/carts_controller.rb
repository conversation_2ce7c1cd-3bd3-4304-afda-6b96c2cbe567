module Api
  # CartsController - Modern RESTful Cart Management
  #
  # Handles HTTP layer for cart operations, delegates business logic to CartsControllerService:
  # - Cart display (GET /api/cart)
  # - Comprehensive cart updates (PUT /api/cart) - items, payment preferences
  #
  class CartsController < Api::BaseController
    include Authenticatable
    include ErrorHandling

    before_action :initialize_services

    # =============================================================================
    # CART OPERATIONS
    # =============================================================================

    def show
      with_error_handling("cart fetch") do
        render_success(@cart_service.get_cart)
      end
    end

    # Comprehensive cart update endpoint (PUT /api/cart)
    # Supports: items, preferred_payment_method
    # Uses enhanced bulk operations with performance monitoring
    def update
      with_error_handling("cart update") do
        result = @cart_service.update_cart(cart_update_params)
        raise StandardError.new(result[:error] || "Cart update failed") unless result[:success]

        render_success(result)
      end
    end

    private

    # =============================================================================
    # INITIALIZATION
    # =============================================================================

    def initialize_services
      @cart_service = CartService.new(@current_user, request.remote_ip)
    end

    def cart_update_params
      params.permit(
        :preferred_payment_method,
        items: [:variant_id, :product_id, :quantity, :price, :title, :image_url, :selected]
      )
    end
  end
end
