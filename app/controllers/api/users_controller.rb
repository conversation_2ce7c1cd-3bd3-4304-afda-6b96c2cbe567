module Api
  class UsersController < Api::BaseController
    include Authenticatable

    # GET /api/users/deletion_check
    def deletion_check
      deletion_service = UserDeletionService.new(@current_user)
      result = deletion_service.can_delete?

      render_success({
        can_delete: result[:can_delete],
        orders_count: result[:orders_count],
        carts_count: result[:carts_count],
        message: result[:message]
      })
    rescue StandardError => e
      render_error("Failed to check deletion eligibility: #{e.message}")
    end

    # DELETE /api/users/account
    def destroy_account
      reason = params[:reason] || "No reason provided"
      deletion_service = UserDeletionService.new(@current_user)

      # Proceed with deletion (no pre-check needed since we auto-cleanup)
      result = deletion_service.delete_account

      if result[:user_deleted]
        render_success({
          message: "Account successfully deleted",
          deletion_id: "del_#{SecureRandom.hex(6)}",
          deleted_at: Time.current.iso8601,
          data_retention_period: "30 days"
        })
      else
        render_error("Account deletion failed", status: :unprocessable_entity)
      end
    rescue StandardError => e
      Rails.logger.error "Account deletion error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      render_error("Account deletion failed: #{e.message}", status: :internal_server_error)
    end

    # GET /api/users/profile
    def profile
      render_success({
        user: user_data(@current_user)
      })
    rescue StandardError => e
      render_error("Failed to fetch user profile: #{e.message}")
    end

    private

    def user_data(user)
      {
        id: user.id.to_s,
        email: user.email,
        name: user.name,
        shopify_customer_id: user.shopify_customer_id,
        created_at: user.created_at,
        updated_at: user.updated_at,
      }
    end
  end
end
