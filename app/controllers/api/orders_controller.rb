module Api
  class OrdersController < BaseController
    include Authenticatable
    include Erro<PERSON><PERSON>andling

    def index
      with_error_handling("order listing") do
        orders = @current_user.orders.order(created_at: :desc)
        render_success(orders.map { |order| order_data(order) })
      end
    end

    def show
      with_error_handling("order lookup", order_id: params[:id]) do
        order = fetch_order!(params[:id])
        render_success(order_data(order))
      end
    end

    def create
      with_error_handling("order checkout", payment_method: checkout_params[:payment_method]) do
        result = checkout_service.checkout(
          payment_method: checkout_params[:payment_method],
          order_params: checkout_params[:order] || {}
        )

        render_success(result)
      end
    end

    def status
      with_error_handling("order status", order_id: params[:order_id]) do
        render_success(order_status_for(params[:order_id]))
      end
    end

    private

    def checkout_service
      @checkout_service ||= OrderCheckoutService.new(@current_user, buyer_ip: request.remote_ip)
    end

    def checkout_params
      params.permit(:payment_method, order: {})
    end

    def order_data(order)
      {
        id: order.id.to_s,
        shopify_order_id: order.shopify_order_id,
        shopify_checkout_url: order.shopify_checkout_url,
        stripe_payment_intent_id: order.stripe_payment_intent_id,
        stripe_client_secret: order.stripe_client_secret,
        stripe_status: order.stripe_status,
        total_price: order.total_price,
        original_total: order.original_total,
        currency: order.currency,
        status: order.status,
        payment_status: order.payment_status,
        payment_method: order.payment_method,
        order_type: order.order_type,
        items: order.items,
        created_at: order.created_at,
        updated_at: order.updated_at
      }
    end

    def fetch_order!(order_id)
      @current_user.orders.find(order_id)
    rescue Mongoid::Errors::DocumentNotFound
      raise StandardError.new("Order not found")
    end

    def fetch_stripe_status(order)
      stripe_service = StripeOrderService.new(@current_user, request.remote_ip)
      stripe_status = stripe_service.check_status(order.stripe_payment_intent_id)

      order.apply_stripe_status!(stripe_status)

      stripe_status_response(order, stripe_status)
    rescue StripeOrderService::CheckoutError => e
      raise StandardError.new("Failed to check payment status: #{e.message}")
    end

    def order_status_for(order_id)
      raise StandardError.new("Order ID is required") if order_id.blank?

      local_order = fetch_order!(order_id)

      return fetch_stripe_status(local_order) if local_order.stripe?

      shopify_order_id = local_order.shopify_order_id
      if shopify_order_id.blank?
        Rails.logger.error "Order #{order_id} (user: #{@current_user.id}) has no shopify_order_id - integration incomplete"
        raise StandardError.new("Unable to verify order status. Order integration incomplete.")
      end

      order_service = Shopify::OrderService.new(
        request.remote_ip,
        user_email: @current_user.email
      )
      status_result = order_service.check_order_status(shopify_order_id)

      local_order.complete_order(status_result) if %w[paid authorized pending].include?(status_result[:payment_status])

      {
        status: status_result[:status],
        payment_status: status_result[:payment_status],
        order_id: order_id,
        local_order_id: local_order.id.to_s,
        shopify_order_name: status_result[:order_name],
        total_price: status_result[:total_price],
        currency: status_result[:currency],
        completed_at: local_order.completed_at,
        order_completed: local_order.status == "completed"
      }
    end

    def stripe_status_response(order, stripe_status)
      {
        status: stripe_status[:status],
        payment_status: order.payment_status,
        order_id: order.id.to_s,
        payment_intent_id: order.stripe_payment_intent_id,
        client_secret: order.stripe_client_secret,
        amount_received: stripe_status[:amount_received],
        currency: stripe_status[:currency] || order.currency,
        completed_at: order.completed_at,
        order_completed: order.status == "completed"
      }
    end
  end
end
