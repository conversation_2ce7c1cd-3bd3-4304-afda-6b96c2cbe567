module Api
  class AuthController < Api::BaseController
    #Use verify token firebase when user login with sso
    def authenticate
      # Extract Firebase token from Authorization header 
      firebase_token = extract_token_from_header

      unless firebase_token
        return render_error("Firebase ID token required")
      end

      result = auth_service.authenticate(firebase_token)

      # Return new standardized format
      render_success({
        backend_token: result[:token],
        user: {
          id: result[:user][:id],
          email: result[:user][:email],
          name: result[:user][:name]
        }
      })
    rescue StandardError => e
      render_error(e.message)
    end

    # Verify token backend check does it expired
    def verify
      # Extract Backend token from Authorization header
      backend_token = extract_token_from_header

      unless backend_token
        return render_error("Authorization token required")
      end

      begin
        decoded = JWT.decode(backend_token, ENV["JWT_SECRET"] || "development_jwt_secret", true, { algorithm: 'HS256' }).first
        user = User.find(decoded["user_id"])

        # Return new standardized format
        render_success({
          valid: true,
          user_id: user.id.to_s
        })
      rescue JWT::DecodeError
        render_error("Invalid or expired token")
      rescue JWT
        ::ExpiredSignature
        render_error("Token has expired")
      rescue Mongoid::Errors::DocumentNotFound
        render_error("User not found")
      rescue => e
        render_error("Token verification failed: #{e.message}")
      end
    end

    private

    def auth_service
      @auth_service ||= AuthService.new
    end

    def extract_token_from_header
      request.headers["Authorization"]&.gsub("Bearer ", "")
    end

    def user_data(user)
      {
        id: user.id.to_s,
        email: user.email,
        name: user.name,
        shopify_customer_id: user.shopify_customer_id,
        created_at: user.created_at,
        updated_at: user.updated_at
      }
    end

    def render_error(message)
      render json: { error: message }, status: :unprocessable_entity
    end

    def extract_token_from_header
      auth_header = request.headers['Authorization']
      return nil unless auth_header&.start_with?('Bearer ')

      auth_header.split(' ').last
    end

    def user_data(user)
      {
        id: user.id.to_s,
        email: user.email,
        name: user.name,
        shopify_customer_id: user.shopify_customer_id,
        firebase_uid: user.firebase_uid,
        created_at: user.created_at,
        updated_at: user.updated_at,
      }
    end
  end
end
