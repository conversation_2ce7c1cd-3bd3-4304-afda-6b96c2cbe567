module Api
  class StripeController < Api::BaseController
    include Authenticatable
    include ErrorHandling

    def config
      with_error_handling("stripe config") do
        render_success({
          enabled: AppSetting.stripe_enabled?,
          publishable_key: ENV["STRIPE_PUBLISHABLE_KEY"],
          payment_methods: AppSetting.enabled_payment_methods,
          environment: Rails.env
        })
      end
    end
  end
end
