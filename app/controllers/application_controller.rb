class ApplicationController < ActionController::API
  # Note: protect_from_forgery is not needed for API-only applications
  # CSRF protection is handled differently for APIs (typically via tokens)

  rescue_from StandardError do |e|
    handle_error(e)
  end

  protected

  def handle_error(error)
    case error
    when Mongoid::Errors::DocumentNotFound
      render json: { error: "Resource not found" }, status: :not_found
    when Mongoid::Errors::Validations
      render json: { error: error.document.errors.full_messages.join(", ") }, status: :unprocessable_entity
    when JWT::DecodeError
      render json: { error: "Invalid token" }, status: :unauthorized
    when AuthenticationExceptions::AuthenticationError
      render json: { error: error.message }, status: :unauthorized
    else
      Rails.logger.error "Unhandled error: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")
      render json: { error: "Internal server error" }, status: :internal_server_error
    end
  end

  def render_success(data, status: :ok)
    render json: { data: data }, status: status
  end

  def render_error(message, status: :unprocessable_entity)
    render json: { error: message }, status: status
  end

  def allow_iframe_requests
    response.headers.delete("X-Frame-Options")
  end
end
