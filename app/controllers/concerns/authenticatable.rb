module Authenticatable
  extend ActiveSupport::Concern

  included do
    before_action :authenticate_user!
  end

  private

  def authenticate_user!
    token = request.headers["Authorization"]&.gsub("Bearer ", "")

    unless token
      return invalid_action!("Authorization required")
    end

    begin
      decoded = JWT.decode(token, ENV["JWT_SECRET"] || "development_jwt_secret").first
      @current_user = User.find(decoded["user_id"])
    rescue JWT::DecodeError, Mongoid::Errors::DocumentNotFound
      invalid_action!("Invalid token")
    end
  end
end
