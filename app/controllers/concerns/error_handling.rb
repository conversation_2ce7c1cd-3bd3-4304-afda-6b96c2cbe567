module ErrorHandling
  extend ActiveSupport::Concern

  private

  # Standardized error logging with context
  def log_controller_error(operation, error, context = {})
    controller_name = self.class.name.demodulize
    user_id = @current_user&.id || 'anonymous'
    
    Rails.logger.error "[#{controller_name}] #{operation} failed: #{error.message}"
    Rails.logger.error "User: #{user_id}"
    
    # Add context information if provided
    context.each do |key, value|
      Rails.logger.error "#{key.to_s.humanize}: #{value}" if value.present?
    end
    
    # Log backtrace for debugging (first 3 lines to avoid spam)
    Rails.logger.error error.backtrace.first(3).join("\n") if error.backtrace
  end

  # Standardized error response with operation context
  def handle_controller_error(operation, error, context = {})
    log_controller_error(operation, error, context)
    render_error(format_error_message(operation, error))
  end

  # Format user-friendly error messages based on operation and error type
  def format_error_message(operation, error)
    case error.message
    when /Cart is empty/
      "Your cart is empty. Please add items before proceeding."
    when /not found in cart/
      "Selected items are no longer available. Please refresh your cart."
    when /exceeds available/
      "Requested quantity exceeds available stock. Please adjust quantities."
    when /Failed to create payment/
      "Payment processing is temporarily unavailable. Please try again."
    when /Unsupported payment method/
      "Selected payment method is not available. Please try a different method."
    when /Invalid token/
      "Your session has expired. Please log in again."
    else
      "#{operation.humanize} failed: #{error.message}"
    end
  end

  # Wrapper for common controller action pattern with error handling
  def with_error_handling(operation, context = {})
    yield
  rescue StandardError => e
    handle_controller_error(operation, e, context)
  end

  # Enhanced error handling specifically for cart operations
  # Includes cart-specific error patterns and bulk operation error handling
  def with_cart_error_handling(operation, context = {})
    yield
  rescue CartOperations::ItemService::BulkOperationError => e
    handle_bulk_operation_error(operation, e, context)
  rescue Cart::VersionConflictError => e
    handle_version_conflict_error(operation, e, context)
  rescue StandardError => e
    handle_cart_operation_error(operation, e, context)
  end

  # Handle bulk operation specific errors
  def handle_bulk_operation_error(operation, error, context = {})
    log_controller_error(operation, error, context.merge(
      successful_items: error.successful_items,
      failed_items: error.failed_items,
      operation_results: error.operation_results
    ))

    # Return partial success response if some items succeeded
    if error.successful_items.any?
      render json: {
        success: false,
        partial_success: true,
        message: "Some items were updated successfully, but others failed",
        successful_items: error.successful_items,
        failed_items: error.failed_items,
        operation_results: error.operation_results
      }, status: :multi_status
    else
      render_error("All cart updates failed: #{error.message}")
    end
  end

  # Handle version conflict errors with retry suggestion
  def handle_version_conflict_error(operation, error, context = {})
    log_controller_error(operation, error, context)
    render json: {
      success: false,
      error: "Cart was updated by another request. Please refresh and try again.",
      error_code: "VERSION_CONFLICT",
      retry_suggested: true
    }, status: :conflict
  end

  # Handle cart-specific operation errors
  def handle_cart_operation_error(operation, error, context = {})
    log_controller_error(operation, error, context)

    # Cart-specific error message formatting
    message = case error.message
    when /Cart version conflict/
      "Your cart was updated by another request. Please refresh and try again."
    when /Bulk update failed/
      "Failed to update cart items. Please try again."
    when /Performance timeout/
      "Cart update is taking longer than expected. Please try again."
    when /Invalid item data/
      "Some cart items have invalid data. Please check your selections."
    else
      format_error_message(operation, error)
    end

    render_error(message)
  end
end
