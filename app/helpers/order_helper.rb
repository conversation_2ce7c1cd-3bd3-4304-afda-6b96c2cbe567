module OrderHelper
  extend ActiveSupport::Concern

  private

  def create_order_from_checkout(result, payment_method, cart)
    order_status = case payment_method
    when "shopify"
      result.dig(:metadata, :is_draft_order) ? "draft" : "pending"
    when "stripe"
      "pending"
    else
      "pending"
    end

    selected_items = cart.items.select { |item| item["selected"] == true }

    order_attributes = {
      user: @current_user,
      total_price: extract_total_price_from_result(result),
      currency: result[:currency] || cart.currency || "USD",
      status: order_status,
      payment_method: payment_method,
      payment_status: "pending",
      items: serialize_cart_items_for_order(selected_items),
      cart_id: cart.id.to_s,
      order_type: payment_method
    }

    case payment_method
    when "shopify"
      shopify_id = result[:order_id] || result[:shopify_checkout_id] || result["orderId"] || result["id"]
      order_attributes[:shopify_order_id] = shopify_id
      order_attributes[:shopify_checkout_url] = result[:checkout_url] || result["webUrl"]
      Rails.logger.info "Setting shopify_order_id: #{shopify_id} for order creation"
    when "stripe"
      order_attributes[:stripe_payment_intent_id] = fetch_result_value(result, :payment_intent_id)
      order_attributes[:stripe_client_secret] = fetch_result_value(result, :client_secret)
      order_attributes[:stripe_checkout_url] = fetch_result_value(result, :checkout_url)
      order_attributes[:stripe_status] = fetch_result_value(result, :status)
      order_attributes[:stripe_status_synced_at] = Time.current
    end

    order = Order.create!(order_attributes)
    Rails.logger.info "Created order record #{order.id} for #{payment_method} checkout"
    order
  rescue StandardError => e
    Rails.logger.error "Failed to create order record: #{e.message}"
    Rails.logger.error e.backtrace.first(5).join("\n")
    raise StandardError.new("Failed to create order record: #{e.message}")
  end

  def extract_total_price_from_result(result)
    return 0.0 unless result.is_a?(Hash)

    if result["totalPrice"]
      result["totalPrice"]["amount"].to_f
    elsif result[:total_price]
      result[:total_price].to_f
    elsif result[:amount_total]
      result[:amount_total].to_f
    elsif result[:amount_total_cents]
      cents_to_amount(result[:amount_total_cents])
    elsif result[:amount_received_cents]
      cents_to_amount(result[:amount_received_cents])
    else
      0.0
    end
  end

  def serialize_cart_items_for_order(cart_items)
    return [] if cart_items.blank?

    cart_items.map do |item|
      {
        "variant_id" => item["variant_id"],
        "product_id" => item["product_id"],
        "quantity" => item["quantity"],
        "price" => item["price"].to_f,
        "title" => item["title"],
        "image_url" => item["image_url"],
        "selected" => item["selected"]
      }
    end
  end

  def fetch_result_value(result, key)
    return if result.blank?

    symbol_value = result[key]
    return symbol_value if symbol_value.present?

    string_value = result[key.to_s]
    return string_value if string_value.present?

    nil
  end

  def cents_to_amount(cents)
    return 0.0 unless cents

    (cents.to_f / 100.0).round(2)
  end
end
