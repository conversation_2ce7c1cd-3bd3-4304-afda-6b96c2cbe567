class OrderCheckoutService
  include Order<PERSON><PERSON><PERSON>

  def initialize(user, buyer_ip: nil)
    @current_user = user
    @buyer_ip = buyer_ip
  end

  def checkout(payment_method:, order_params: {})
    normalized_method = payment_method.to_s.downcase
    validate_payment_method!(normalized_method)

    cart = find_or_create_active_cart
    items = cart.items.select { |item| item["selected"] == true }
    raise StandardError.new("No items selected for checkout") if items.empty?

    case normalized_method
    when "shopify"
      process_shopify_checkout(items, order_params, cart)
    when "stripe"
      process_stripe_checkout(items, cart)
    else
      raise StandardError.new("Unsupported payment method: #{payment_method}")
    end
  end

  private

  def validate_payment_method!(payment_method)
    valid_methods = AppSetting.enabled_payment_methods
    return if valid_methods.include?(payment_method)

    raise StandardError.new("Invalid payment method. Must be one of: #{valid_methods.join(', ')}")
  end

  def find_or_create_active_cart
    @current_user.carts.where(status: "active").first ||
      @current_user.carts.create!(status: "active")
  end

  def process_shopify_checkout(items, options, cart)
    order_service = Shopify::OrderService.new(
      @buyer_ip,
      user_email: @current_user.email
    )

    checkout_result = order_service.create_checkout(
      items,
      @current_user.shopify_customer_id,
      nil,
      @current_user.email
    )

    raise StandardError.new("Failed to create Shopify checkout") unless checkout_result

    order = create_order_from_checkout(checkout_result, "shopify", cart)
    raise StandardError.new("Failed to create order record") unless order

    {
      success: true,
      order_id: order.id.to_s,
      checkout_url: checkout_result["webUrl"],
      payment_method: "shopify",
      status: "pending"
    }
  end

  def process_stripe_checkout(items, cart)
    raise StandardError.new("Stripe payments are not enabled") unless AppSetting.stripe_enabled?

    stripe_service = StripeOrderService.new(@current_user, @buyer_ip)

    stripe_result = stripe_service.create_checkout(
      items,
      cart_id: cart.id.to_s,
      customer_email: @current_user.email,
      currency: cart.currency
    )

    order = create_order_from_checkout(stripe_result, "stripe", cart)
    raise StandardError.new("Failed to create order record for Stripe checkout") unless order

    order.update!(
      stripe_payment_intent_id: stripe_result[:payment_intent_id],
      stripe_client_secret: stripe_result[:client_secret],
      stripe_status: stripe_result[:status],
      stripe_status_synced_at: Time.current
    )

    {
      success: true,
      order_id: order.id.to_s,
      payment_method: "stripe",
      payment_intent_id: stripe_result[:payment_intent_id],
      client_secret: stripe_result[:client_secret],
      status: stripe_result[:status],
      amount: stripe_result[:amount_total],
      currency: stripe_result[:currency]
    }
  rescue StripeOrderService::MissingConfigError => e
    Rails.logger.error "Stripe configuration error: #{e.message}"
    raise StandardError.new("Stripe configuration missing on server")
  rescue StripeOrderService::CheckoutError => e
    Rails.logger.error "Stripe checkout failed: #{e.message}"
    raise e
  end
end
