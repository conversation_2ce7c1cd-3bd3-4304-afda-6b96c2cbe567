module CartOperations
  # Cart Item Service
  #
  # Handles complex cart item operations with fail-fast approach:
  # - Atomic bulk item updates with optimistic locking
  # - Atomic bulk item removal with strict error handling
  # - Strict validation for multiple items - all succeed or all fail
  #
  # Note: Simple operations (add, remove, update single items) should use Cart model directly
  #
  class ItemService
    # Custom exceptions for better error handling
    class ItemError < StandardError; end
    class ValidationError < StandardError; end

    def initialize(user)
      @user = user
    end

    # Client-trusted full cart replacement - simple and fast
    #
    # @param cart [Cart] The cart to update
    # @param items_data [Array<Hash>] Complete cart items from client
    # @return [Boolean] True if replacement succeeded
    def update_multiple_items(cart, items_data, options = {})
      cart.replace_items!(items_data)
      true
    end

    # Removes multiple items atomically - all succeed or all fail
    #
    # @param cart [Cart] The cart to update
    # @param variant_ids [Array<String>] Array of variant IDs to remove
    # @param options [Hash] Operation options
    # @return [Boolean] True if all items removed successfully
    # @raise [ValidationError] If any validation fails
    # @raise [ItemError] If any item removal fails
    def remove_multiple_items(cart, variant_ids, options = {})
      perform_bulk_remove(cart, variant_ids, options)
      true
    end

    # Validates multiple items - fails on first validation error
    #
    # @param items_data [Array<Hash>] Array of item data to validate
    # @return [Boolean] True if all items are valid
    # @raise [ValidationError] If any validation fails
    def validate_multiple_items(items_data)
      perform_bulk_validation(items_data)
      true
    end

    # Calculates total for a set of items
    #
    # @param items [Array] Items to calculate total for
    # @return [Float] Total price
    def calculate_items_total(items)
      items.sum { |item| item["price"] * item["quantity"] }
    end
  end
end
