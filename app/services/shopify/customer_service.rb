module Shopify

  class CustomerService
    include ErrorLogging
    include Documentable

    def initialize(buyer_ip = nil)
      @graphql_service = GraphQL::GraphQLService.new(buyer_ip)
    end

    # Creates a new customer in Shopify
    def create_customer(email, first_name = nil, last_name = nil)
      validate_email_format_for_service!(email)

      Rails.logger.info "Creating Shopify customer for email: #{email}"

      query = <<~GRAPHQL
        mutation customerCreate($input: CustomerInput!) {
          customerCreate(input: $input) {
            customer {
              id
              email
              firstName
              lastName
              createdAt
            }
            userErrors {
              field
              message
            }
          }
        }
      GRAPHQL

      variables = {
        input: build_customer_input(email, first_name, last_name)
      }

      response = @graphql_service.make_admin_request(query, variables)
      process_customer_creation_response(response, email)
    end

    # Finds a customer by email address
    def find_customer_by_email(email)
      validate_email_format_for_service!(email)

      Rails.logger.info "Finding Shopify customer by email: #{email}"

      query = <<~GRAPHQL
        query getCustomerByEmail($query: String!) {
          customers(first: 1, query: $query) {
            edges {
              node {
                id
                email
                firstName
                lastName
                createdAt
                state
              }
            }
          }
        }
      GRAPHQL

      variables = { query: "email:#{email}" }
      response = @graphql_service.make_admin_request(query, variables)

      customers = response.dig("data", "customers", "edges")
      return nil if customers.blank?

      customer_node = customers.first["node"]
      format_customer_data(customer_node)
    end

    # Deactivates a customer in Shopify
    def deactivate_customer(customer_id)
      validate_customer_id!(customer_id)

      Rails.logger.info "Deactivating Shopify customer: #{customer_id}"

      query = <<~GRAPHQL
        mutation customerUpdate($input: CustomerInput!) {
          customerUpdate(input: $input) {
            customer {
              id
              state
            }
            userErrors {
              field
              message
            }
          }
        }
      GRAPHQL

      variables = {
        input: {
          id: customer_id,
          state: "DISABLED"
        }
      }

      response = @graphql_service.make_admin_request(query, variables)
      process_customer_deactivation_response(response, customer_id)
    end

    private

    # Validate email format using Documentable concern
    # This replaces the duplicate email validation logic
    def validate_email_format_for_service!(email)
      if email.blank?
        raise "Email is required for customer operations"
      end

      # Create a temporary object to use Documentable validation
      validator = Class.new do
        include Documentable
        attr_accessor :email

        def initialize(email)
          @email = email
        end
      end.new(email)

      unless validator.validate_email_format(:email)
        raise "Invalid email format: #{email}"
      end
    end

    def validate_customer_id!(customer_id)
      if customer_id.blank?
        raise "Customer ID is required"
      end
    end

    def build_customer_input(email, first_name, last_name)
      input = { email: email }
      input[:firstName] = first_name if first_name.present?
      input[:lastName] = last_name if last_name.present?
      input
    end

    def process_customer_creation_response(response, email)
      customer_data = response.dig("data", "customerCreate")
      
      if customer_data["userErrors"].any?
        handle_customer_creation_errors(customer_data["userErrors"], email)
      else
        customer = customer_data["customer"]
        Rails.logger.info "Successfully created customer: #{customer['id']}"
        format_customer_data(customer)
      end
    end

    def handle_customer_creation_errors(errors, email)
      error_messages = errors.map { |error| "#{error['field']}: #{error['message']}" }.join(', ')
      
      # Handle duplicate email error by attempting to find existing customer
      if errors.any? { |error| error['message'].include?('taken') || error['message'].include?('already exists') }
        Rails.logger.info "Customer with email #{email} already exists, attempting to find existing customer"
        existing_customer = find_customer_by_email(email)
        return existing_customer if existing_customer
      end

      Rails.logger.error "Customer creation failed for email: #{email}. Errors: #{error_messages}"
      raise "Failed to create customer: #{error_messages}"
    end

    def process_customer_deactivation_response(response, customer_id)
      customer_data = response.dig("data", "customerUpdate")
      
      if customer_data["userErrors"].any?
        error_messages = customer_data["userErrors"].map { |error| error['message'] }.join(', ')
        Rails.logger.error "Customer deactivation failed for ID: #{customer_id}. Errors: #{error_messages}"
        raise "Failed to deactivate customer: #{error_messages}"
      else
        Rails.logger.info "Successfully deactivated customer: #{customer_id}"
        true
      end
    end

    def format_customer_data(customer_node)
      {
        id: customer_node["id"],
        email: customer_node["email"],
        first_name: customer_node["firstName"],
        last_name: customer_node["lastName"],
        created_at: customer_node["createdAt"],
        state: customer_node["state"]
      }
    end
  end
end
