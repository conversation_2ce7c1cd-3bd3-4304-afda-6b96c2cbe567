module Shopify
  class OrderService
    include ErrorLogging

    def initialize(buyer_ip = nil, user_email: nil)
      @buyer_ip = buyer_ip
      @user_email = user_email
      @graphql_service = GraphQL::GraphQLService.new(buyer_ip)
    end

    def create_checkout(cart_items, customer_id = nil, customer_access_token = nil, customer_email = nil)
      draft_order = attempt_draft_order_creation(cart_items, customer_id, customer_email)
      return draft_order if draft_order
      raise "Failed to create checkout using available methods"
    end

    def check_order_status(order_reference)
      Rails.logger.info "Checking Shopify order status for: #{order_reference}"

      # Handle nil or empty order reference
      if order_reference.blank?
        Rails.logger.warn "Order reference is blank, returning pending status"
        return format_pending_status(nil, nil)
      end

      # Determine the type of order reference
      if order_reference.include?('Cart')
        Rails.logger.info "Detected Storefront Cart ID, checking cart status"
        check_storefront_cart_status(order_reference)
      elsif order_reference.include?('DraftOrder')
        Rails.logger.info "Detected Draft Order ID, checking draft order status"
        check_draft_order_status(order_reference)
      elsif order_reference.include?('Order')
        Rails.logger.info "Detected regular Order ID, checking order status"
        check_regular_order_status(order_reference)
      else
        Rails.logger.info "Unknown order reference format, trying all methods"
        #throw error
        raise "Unknown order reference format: #{order_reference}"
      end
    rescue StandardError => e
      Rails.logger.error "Failed to check order status: #{e.message}"
      {
        status: 'unknown',
        payment_status: 'unknown',
        shopify_order_id: order_reference,
        error: e.message
      }
    end


    private

    def validate_cart_item!(item, index)
      required_fields = %w[variant_id quantity]
      missing_fields = required_fields.select { |field| item[field].blank? && item[field.to_sym].blank? }

      if missing_fields.any?
        raise "Cart item #{index} missing required fields: #{missing_fields.join(', ')}"
      end

      quantity = (item["quantity"] || item[:quantity]).to_i
      if quantity <= 0
        raise "Cart item #{index} must have quantity greater than 0"
      end
    end

    #create draft order
    def attempt_draft_order_creation(cart_items, customer_id, customer_email)
      Rails.logger.info "Attempting Draft Order creation"

      query = build_draft_order_query
      variables = build_draft_order_variables(cart_items, customer_id, customer_email)

      response = @graphql_service.make_admin_request(query, variables)
      process_draft_order_response(response)
    end

    def build_draft_order_variables(cart_items, customer_id, customer_email)
      line_items = cart_items.map do |item|
        {
          variantId: format_variant_id(item["variant_id"] || item[:variant_id]),
          quantity: (item["quantity"] || item[:quantity]).to_i
        }
      end

      input = { lineItems: line_items }
      
      # Add customer if available
      if customer_id.present?
        input[:customerId] = format_customer_id(customer_id)
      elsif customer_email.present?
        input[:email] = customer_email
      end

      { input: input }
    end

    def process_draft_order_response(response)
      draft_order_data = response.dig("data", "draftOrderCreate")

      if draft_order_data["userErrors"].any?
        error_messages = draft_order_data["userErrors"].map { |error| error['message'] }.join(', ')
        Rails.logger.error "Draft order creation errors: #{error_messages}"
        raise "Draft order creation failed: #{error_messages}"
      end

      draft_order = draft_order_data["draftOrder"]
      Rails.logger.info "Successfully created draft order: #{draft_order['id']}"

      {
        "id" => draft_order["id"],
        "webUrl" => draft_order["invoiceUrl"],
        "totalPrice" => {
          "amount" => draft_order["totalPrice"],
          "currencyCode" => draft_order["currencyCode"]
        },
        "isDraft" => true,
        "orderId" => draft_order.dig("order", "id")
      }
    end

    def check_regular_order_status(order_id)
      Rails.logger.info "Checking regular order status for: #{order_id}"

      query = <<~GRAPHQL
        query getOrder($id: ID!) {
          order(id: $id) {
            id
            name
            displayFinancialStatus
            displayFulfillmentStatus
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            createdAt
            updatedAt
          }
        }
      GRAPHQL

      variables = { id: order_id }
      response = @graphql_service.make_admin_request(query, variables)

      order = response.dig("data", "order")
      return nil unless order

      format_regular_order_status(order)
    rescue StandardError => e
      Rails.logger.error "Failed to check regular order status: #{e.message}"
      format_error_status(order_id, e.message)
    end

    def check_storefront_cart_status(cart_id)
      Rails.logger.info "Checking storefront cart status for: #{cart_id}"

      # Clean cart ID for matching
      clean_cart_id = cart_id.gsub(/^gid:\/\/shopify\/Cart\//, "")

      # Method 1: Check if cart was converted to an order
      recent_orders = find_recent_orders_for_user(clean_cart_id)

      if recent_orders.any?
        Rails.logger.info "Found #{recent_orders.length} recent orders, checking for matches"

        # Look for an order that might match this cart
        matching_order = find_matching_order(recent_orders, clean_cart_id)

        if matching_order
          Rails.logger.info "Found matching order: #{matching_order['name']}"
          return format_completed_order_status(matching_order, clean_cart_id)
        end
      end

      # Return pending status if no matching order found
      format_pending_status(cart_id, clean_cart_id)
    end



    def find_matching_order(orders, cart_id)
      # Simple heuristic: Find the most recent order that matches our user's email
      user_email = get_current_user_email
      return nil unless user_email

      Rails.logger.info "Looking for orders matching email: #{user_email}"

      matching_orders = orders.select do |order|
        order['customer'] && order['customer']['email'] == user_email
      end

      if matching_orders.any?
        # Return the most recent matching order
        matching_orders.sort_by { |order| order['createdAt'] }.last
      else
        nil
      end
    end
    
    def get_current_user_email
      @user_email
    end

    def map_shopify_financial_status(status)
      case status&.downcase
      when 'paid'
        'paid'
      when 'pending'
        'pending'
      when 'authorized'
        'authorized'
      when 'partially_paid'
        'partially_paid'
      when 'refunded'
        'refunded'
      when 'voided'
        'voided'
      else
        'unknown'
      end
    end

    def format_completed_order_status(order, cart_id)
      {
        status: 'completed',
        payment_status: map_shopify_financial_status(order['displayFinancialStatus']),
        shopify_order_id: order['id'],
        order_name: order['name'],
        total_price: order['totalPriceSet']['shopMoney']['amount'],
        currency: order['totalPriceSet']['shopMoney']['currencyCode'],
        cart_id: cart_id
      }
    end

    def format_pending_status(cart_id, clean_cart_id)
      {
        status: 'pending',
        payment_status: 'pending',
        shopify_order_id: cart_id || 'unknown',
        order_name: nil,
        total_price: nil,
        currency: 'USD',
        cart_id: clean_cart_id
      }
    end

    def format_error_status(order_id, error_message)
      {
        status: 'error',
        payment_status: 'error',
        shopify_order_id: order_id,
        error: error_message
      }
    end

    def format_draft_order_status(draft_order)
      if draft_order['order']
        # Draft order has been converted to a real order
        {
          status: 'completed',
          payment_status: map_shopify_financial_status(draft_order['order']['displayFinancialStatus']),
          shopify_order_id: draft_order['order']['id'],
          order_name: draft_order['order']['name'],
          total_price: draft_order['totalPrice'],
          currency: draft_order['currencyCode'],
          created_at: draft_order['createdAt'],
          updated_at: draft_order['updatedAt']
        }
      else
        # Still a draft order
        {
          status: 'draft',
          payment_status: 'pending',
          shopify_order_id: draft_order['id'],
          order_name: draft_order['name'],
          total_price: draft_order['totalPrice'],
          currency: draft_order['currencyCode'],
          created_at: draft_order['createdAt'],
          updated_at: draft_order['updatedAt']
        }
      end
    end

    def format_regular_order_status(order)
      {
        status: 'completed',
        payment_status: map_shopify_financial_status(order['displayFinancialStatus']),
        shopify_order_id: order['id'],
        order_name: order['name'],
        total_price: order['totalPriceSet']['shopMoney']['amount'],
        currency: order['totalPriceSet']['shopMoney']['currencyCode'],
        created_at: order['createdAt'],
        updated_at: order['updatedAt']
      }
    end

    # Build GraphQL query for draft order creation
    def build_draft_order_query
      <<~GRAPHQL
        mutation draftOrderCreate($input: DraftOrderInput!) {
          draftOrderCreate(input: $input) {
            draftOrder {
              id
              invoiceUrl
              totalPrice
              currencyCode
              order {
                id
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      GRAPHQL
    end

    # Format variant ID for GraphQL
    def format_variant_id(variant_id)
      return variant_id if variant_id.start_with?('gid://shopify/ProductVariant/')
      "gid://shopify/ProductVariant/#{variant_id}"
    end

    # Format customer ID for GraphQL
    def format_customer_id(customer_id)
      return customer_id if customer_id.start_with?('gid://shopify/Customer/')
      "gid://shopify/Customer/#{customer_id}"
    end

    # Find recent orders for user (placeholder implementation)
    def find_recent_orders_for_user(cart_id)
      # This would typically query Shopify for recent orders
      # For now, return empty array to prevent errors
      []
    end

    # Find order by ID (placeholder implementation)
    def find_order(order_id)
      # This would typically query the database for the order
      # For now, return a basic order structure
      { id: order_id, payment_method: 'shopify' }
    end

    # Check draft order status (placeholder implementation)
    def check_draft_order_status(order_id)
      Rails.logger.info "Checking draft order status for: #{order_id}"
      # This would typically query Shopify for draft order status
      # For now, return a basic status
      {
        status: 'pending',
        payment_status: 'pending',
        shopify_order_id: order_id
      }
    end

    # Detect payment method from order (placeholder implementation)
    def detect_payment_method(order)
      order[:payment_method] || 'shopify'
    end
  end
end