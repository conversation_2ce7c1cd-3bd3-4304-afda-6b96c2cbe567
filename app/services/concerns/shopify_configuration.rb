module ShopifyConfiguration
  extend ActiveSupport::Concern

  private

  # Load and validate Shopify configuration from environment variables
  def load_shopify_configuration
    config = {
      shop_name: ENV["SHOPIFY_SHOP_NAME"],
      admin_access_token: <PERSON>NV["SHOPIFY_ACCESS_TOKEN"],
      storefront_access_token: ENV["SHOPIFY_STOREFRONT_ACCESS_TOKEN"]
    }

    validate_shopify_configuration!(config)
    config
  end

  # Validate Shopify configuration 
  def validate_shopify_configuration!(config)
    # Admin access token and shop name are required
    required_keys = [:shop_name, :admin_access_token]
    missing_required = required_keys.select { |key| config[key].blank? }

    if missing_required.any?
      raise "Missing required Shopify configuration: #{missing_required.join(', ')}"
    end

    # Warn if storefront access token is missing (limits storefront functionality)
    if config[:storefront_access_token].blank?
      Rails.logger.warn "SHOPIFY_STOREFRONT_ACCESS_TOKEN not configured - Storefront Cart API will be disabled"
    end
  end
end
