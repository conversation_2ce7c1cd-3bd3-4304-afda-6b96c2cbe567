# Handles user authentication and JWT token management
class AuthService
  class AuthenticationError < StandardError; end
  class TokenError < AuthenticationError; end
  class UserCreationError < AuthenticationError; end
  class ShopifyIntegrationError < AuthenticationError; end

  def initialize(firebase_auth_service: FirebaseAuthService.new, shopify_service: ShopifyService.new)
    @firebase_auth_service = firebase_auth_service
    @shopify_service = shopify_service
  end

  def authenticate(token)
    validate_token!(token)

    firebase_user = verify_firebase_token(token)
    user = find_or_create_user(firebase_user)
    jwt_token = generate_jwt_token(user)

    build_auth_response(jwt_token, user)
  rescue StandardError => e
    handle_authentication_error(e)
  end

  private

  def validate_token!(token)
    raise TokenError, "Authorization token is missing" if token.blank?
  end

  def verify_firebase_token(token)
    @firebase_auth_service.verify_token(token)
  rescue StandardError => e
    raise e.message
  end

  def find_or_create_user(firebase_user)
    user = find_existing_user(firebase_user["localId"])

    if user
      handle_existing_user(user)
    else
      create_new_user(firebase_user)
    end
  end

  def find_existing_user(firebase_uid)
    User.where(firebase_uid: firebase_uid).first
  end

  def handle_existing_user(user)
    return user if user.shopify_customer_id.present?

    shopify_customer_id = ensure_shopify_customer_for_user(user)
    update_user_shopify_id(user, shopify_customer_id)

    user
  end

  def create_new_user(firebase_user)
    shopify_customer_id = ensure_shopify_customer_for_firebase_user(firebase_user)

    user_params = build_user_params(firebase_user, shopify_customer_id)
    user = User.create(user_params)

    validate_user_creation!(user)
    user
  end

  def ensure_shopify_customer_for_user(user)
    existing_customer = find_shopify_customer(user.email)
    return existing_customer["id"] if existing_customer

    create_shopify_customer(user.email, user.name)["id"]
  end

  def ensure_shopify_customer_for_firebase_user(firebase_user)
    email = firebase_user["email"]
    name = firebase_user["displayName"]

    existing_customer = find_shopify_customer(email)
    return existing_customer["id"] if existing_customer

    create_shopify_customer(email, name)["id"]
  end

  def find_shopify_customer(email)
    @shopify_service.find_customer_by_email(email)
  rescue StandardError => e
    Rails.logger.warn "Failed to find Shopify customer: #{e.message}"
    nil
  end

  def create_shopify_customer(email, name)
    @shopify_service.create_customer(email: email, name: name)
  rescue StandardError => e
    raise e.message
  end

  def update_user_shopify_id(user, shopify_customer_id)
    user.update!(shopify_customer_id: shopify_customer_id)
  rescue StandardError => e
    raise UserCreationError, "Failed to update user with Shopify ID: #{e.message}"
  end

  def build_user_params(firebase_user, shopify_customer_id)
    {
      firebase_uid: firebase_user["localId"],
      email: firebase_user["email"],
      name: firebase_user["displayName"],
      shopify_customer_id: shopify_customer_id
    }
  end

  def validate_user_creation!(user)
    unless user.persisted?
      raise UserCreationError, "Failed to create user: #{user.errors.full_messages.join(', ')}"
    end
  end

  def generate_jwt_token(user)
    payload = build_jwt_payload(user)
    secret = jwt_secret

    JWT.encode(payload, secret)
  rescue StandardError => e
    raise TokenError, "Failed to generate JWT token: #{e.message}"
  end

  def build_jwt_payload(user)
    {
      user_id: user.id.to_s,
      email: user.email,
      exp: AppSetting.jwt_expiration.from_now.to_i,
      iat: Time.current.to_i
    }
  end

  def jwt_secret
    ENV["JWT_SECRET"] || Rails.application.credentials.jwt_secret || "development_jwt_secret"
  end

  def build_auth_response(jwt_token, user)
    {
      token: jwt_token,
      user: format_user_data(user)
    }
  end

  def format_user_data(user)
    {
      id: user.id.to_s,
      email: user.email,
      name: user.name,
      shopify_customer_id: user.shopify_customer_id,
      created_at: user.created_at,
      updated_at: user.updated_at
    }
  end

  def handle_authentication_error(error)
    case error
    when TokenError, UserCreationError, ShopifyIntegrationError
      raise error
    else
      Rails.logger.error "Authentication failed: #{error.message}"
      raise error.message
    end
  end
end
