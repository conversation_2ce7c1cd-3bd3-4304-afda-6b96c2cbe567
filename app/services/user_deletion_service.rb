class UserDeletionService
  def initialize(user, firebase_auth_service: FirebaseAuthService.new, shopify_service: ShopifyService.new)
    @user = user
    @firebase_auth_service = firebase_auth_service
    @shopify_service = shopify_service
  end

  def delete_account
    validate_user_for_deletion

    deletion_result = {
      user_deleted: false,
      firebase_deleted: false,
      shopify_deleted: false,
      orders_cleaned: false,
      carts_cleaned: false,
      errors: []
    }

    begin
      # Step 1: Clean up orders and carts first
      cleanup_user_data
      deletion_result[:orders_cleaned] = true
      deletion_result[:carts_cleaned] = true

      # Step 2: Delete from Firebase (invalidates all tokens)
      delete_firebase_user
      deletion_result[:firebase_deleted] = true

      # Step 3: Deactivate Shopify customer
      handle_shopify_customer
      deletion_result[:shopify_deleted] = true

      # Step 4: Delete local user data
      delete_local_user_data
      deletion_result[:user_deleted] = true

      deletion_result
    rescue StandardError => e
      Rails.logger.error "Account deletion failed: #{e.message}"
      Rails.logger.error "Backtrace: #{e.backtrace.first(5).join("\n")}" if e.backtrace
      deletion_result[:errors] << e.message

      if deletion_result[:firebase_deleted] && !deletion_result[:user_deleted]
        deletion_result[:errors] << "Firebase user deleted but local cleanup failed. Manual intervention may be required."
      end

      deletion_result
    end
  end

  def can_delete?
    # Always allow deletion - we'll clean up orders and carts automatically
    {
      can_delete: true,
      orders_count: @user.orders.count,
      carts_count: @user.carts.count,
      message: "Account can be deleted. Orders and carts will be automatically removed."
    }
  end

  private

  def validate_user_for_deletion
    raise "User is required" unless @user
    raise "User must have Firebase UID" unless @user.firebase_uid.present?
    # No longer check for pending orders/carts - we'll clean them up automatically
  end

  def cleanup_user_data
    # Clean up all orders
    if @user.orders.exists?
      orders_count = @user.orders.count
      @user.orders.destroy_all
      Rails.logger.info "Deleted #{orders_count} orders for user #{@user.id}"
    end

    # Clean up all carts
    if @user.carts.exists?
      carts_count = @user.carts.count
      @user.carts.destroy_all
      Rails.logger.info "Deleted #{carts_count} carts for user #{@user.id}"
    end
  end

  def delete_firebase_user
    return unless @user.firebase_uid.present?
    
    begin
      @firebase_auth_service.delete_user(@user.firebase_uid)
    rescue StandardError => e
      if e.message.include?("USER_NOT_FOUND") || e.message.include?("not found")
        Rails.logger.info "Firebase user not found during deletion (continuing): #{e.message}"
      else
        raise "Failed to delete Firebase user: #{e.message}"
      end
    end
  end

  def handle_shopify_customer
    return unless @user.shopify_customer_id.present?
    
    begin
      @shopify_service.deactivate_customer(@user.shopify_customer_id)
    rescue StandardError => e
      if e.message.include?("404") || e.message.include?("not found")
        Rails.logger.info "Shopify customer not found during deletion (continuing): #{e.message}"
      else
        Rails.logger.warn "Failed to deactivate Shopify customer (continuing): #{e.message}"
      end
    end
  end

  def delete_local_user_data
    @user.destroy!
    
    unless @user.destroyed?
      raise "Failed to delete user from local database"
    end
  end

  def has_pending_orders?
    return false unless @user.orders.exists?
    
    pending_statuses = %w[pending processing]
    @user.orders.where(status: { "$in" => pending_statuses }).exists?
  end

  def has_active_cart_items?
    return false unless @user.carts.exists?
    
    @user.carts.any? { |cart| cart.items.present? && cart.items.any? }
  end
end
