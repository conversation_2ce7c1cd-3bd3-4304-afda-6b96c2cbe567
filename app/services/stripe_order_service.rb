require "stripe"

class StripeOrderService
  class MissingConfigError < StandardError; end
  class CheckoutError < StandardError; end

  def initialize(user, request_ip = nil)
    @user = user
    @request_ip = request_ip
    configure_stripe!
  end

  def create_checkout(items, options = {})
    validate_items!(items)

    intent_params = build_payment_intent_params(items, options)
    intent = Stripe::PaymentIntent.create(intent_params)

    {
      success: true,
      payment_intent_id: intent.id,
      client_secret: intent.client_secret,
      status: intent.status,
      amount_total_cents: intent.amount,
      amount_total: cents_to_amount(intent.amount),
      currency: intent.currency,
      metadata: intent.metadata&.to_hash || {},
      order_reference: intent.metadata&.[]("order_id")
    }
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe checkout failed: #{e.message}"
    raise CheckoutError, e.message
  end

  def check_status(payment_intent_id)
    intent = Stripe::PaymentIntent.retrieve(payment_intent_id)

    {
      status: intent.status,
      amount_received_cents: intent.amount_received,
      amount_received: cents_to_amount(intent.amount_received),
      currency: intent.currency,
      latest_charge: (intent.latest_charge if intent.respond_to?(:latest_charge)),
      metadata: intent.metadata&.to_hash || {}
    }
  rescue Stripe::InvalidRequestError => e
    Rails.logger.error "Stripe status check failed: #{e.message}"
    raise CheckoutError, e.message
  end

  private

  def configure_stripe!
    api_key = ENV["STRIPE_SECRET_KEY"]
    raise MissingConfigError, "Stripe secret key not configured" if api_key.blank?

    Stripe.api_key = api_key
  end

  def validate_items!(items)
    if items.blank?
      raise CheckoutError, "Items cannot be empty for Stripe checkout"
    end
  end

  def build_payment_intent_params(items, options)
    amount_cents = calculate_amount_cents(items, options)
    currency = options[:currency] || "usd"

    {
      amount: amount_cents,
      currency: currency,
      automatic_payment_methods: { enabled: true },
      metadata: metadata_for_intent(options),
      receipt_email: receipt_email(options)
    }.tap do |params|
      if options[:customer_id].present?
        params[:customer] = options[:customer_id]
      end

      if options[:payment_method_types].present?
        params[:payment_method_types] = options[:payment_method_types]
      end

      if options[:description].present?
        params[:description] = options[:description]
      end
    end
  end

  def calculate_amount_cents(items, options)
    subtotal = items.sum do |item|
      (item["price"] || item[:price]).to_f * (item["quantity"] || item[:quantity]).to_i
    end

    discount_cents = (options[:discount_amount].to_f * 100).round
    amount_cents = (subtotal * 100).round - discount_cents
    [amount_cents, 1].max
  end

  def cents_to_amount(cents)
    return 0.0 unless cents

    (cents.to_f / 100.0).round(2)
  end

  def metadata_for_intent(options)
    {
      user_id: @user&.id&.to_s,
      user_email: @user&.email,
      cart_id: options[:cart_id],
      source: "orders_controller_checkout"
    }.compact
  end

  def receipt_email(options)
    options[:customer_email] || @user&.email
  end
end
