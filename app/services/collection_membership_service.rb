class CollectionMembershipService
  def initialize
    @client = ShopifyGraphQLClient.new
  end

  # Check if products belong to specific collections
  def products_in_collections?(product_ids, collection_ids)
    return false if product_ids.empty? || collection_ids.empty?

    memberships = get_collection_memberships(collection_ids, product_ids)
    memberships.any?
  end

  # Get all products that belong to any of the specified collections
  def get_products_in_collections(collection_ids, product_ids = nil)
    return [] if collection_ids.empty?

    eligible_products = Set.new
    
    collection_ids.each do |collection_id|
      shopify_collection_id = extract_shopify_id(collection_id)
      collection_products = get_collection_products(shopify_collection_id)
      
      if product_ids
        # Filter to only requested products
        eligible_products.merge(collection_products & product_ids)
      else
        eligible_products.merge(collection_products)
      end
    end
    
    eligible_products.to_a
  end

  # Get products in a specific collection with caching
  def get_collection_products(collection_id, use_cache: true)
    cache_key = "collection_products_#{collection_id}"
    
    if use_cache
      cached_products = Rails.cache.read(cache_key)
      return cached_products if cached_products
    end

    products = fetch_collection_products_from_shopify(collection_id)

    # Cache using AppSetting configuration
    Rails.cache.write(cache_key, products, expires_in: AppSetting.cache_expiration) if use_cache
    
    products
  end

  # Get collection information including product count
  def get_collection_info(collection_id)
    shopify_collection_id = extract_shopify_id(collection_id)
    cache_key = "collection_info_#{shopify_collection_id}"
    
    cached_info = Rails.cache.read(cache_key)
    return cached_info if cached_info

    info = fetch_collection_info_from_shopify(shopify_collection_id)

    # Cache using AppSetting configuration
    Rails.cache.write(cache_key, info, expires_in: AppSetting.cache_expiration)
    
    info
  end

  # Bulk check collection memberships for multiple products
  def bulk_check_memberships(collection_ids, product_ids)
    return {} if collection_ids.empty? || product_ids.empty?

    memberships = {}
    
    collection_ids.each do |collection_id|
      shopify_collection_id = extract_shopify_id(collection_id)
      collection_products = get_collection_products(shopify_collection_id)
      
      product_ids.each do |product_id|
        memberships[product_id] ||= []
        if collection_products.include?(product_id)
          memberships[product_id] << collection_id
        end
      end
    end
    
    memberships
  end

  # Clear collection cache (useful for testing or when collections change)
  def clear_collection_cache(collection_id = nil)
    if collection_id
      shopify_collection_id = extract_shopify_id(collection_id)
      Rails.cache.delete("collection_products_#{shopify_collection_id}")
      Rails.cache.delete("collection_info_#{shopify_collection_id}")
    else
      # Clear all collection caches
      Rails.cache.delete_matched("collection_*")
    end
  end

  private

  # Get collection memberships for discount validation
  def get_collection_memberships(collection_ids, product_ids)
    eligible_products = Set.new
    
    collection_ids.each do |collection_id|
      shopify_collection_id = extract_shopify_id(collection_id)
      collection_products = get_collection_products(shopify_collection_id)
      eligible_products.merge(collection_products & product_ids)
    end
    
    eligible_products.to_a
  end

  # Fetch collection products from Shopify API
  def fetch_collection_products_from_shopify(collection_id)
    Rails.logger.info "Fetching products for collection #{collection_id} from Shopify API"
    
    query = <<~GRAPHQL
      query getCollectionProducts($id: ID!, $first: Int!, $after: String) {
        collection(id: "gid://shopify/Collection/#{collection_id}") {
          id
          title
          products(first: $first, after: $after) {
            edges {
              node {
                id
                legacyResourceId
                title
                status
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      }
    GRAPHQL

    products = []
    cursor = nil
    
    begin
      loop do
        variables = {
          id: "gid://shopify/Collection/#{collection_id}",
          first: 250,
          after: cursor
        }

        response = @client.query(query, variables)

        collection_data = response.body.dig("data", "collection")
        if collection_data
          edges = collection_data.dig("products", "edges") || []

          edges.each do |edge|
            node = edge["node"]
            # Only include active products
            if node["status"] == 'ACTIVE'
              product_id = node["legacyResourceId"]
              products << product_id if product_id
            end
          end

          # Check for more pages
          page_info = collection_data.dig("products", "pageInfo")
          if page_info && page_info["hasNextPage"]
            cursor = page_info["endCursor"]
          else
            break
          end
        else
          Rails.logger.warn "Collection #{collection_id} not found"
          break
        end
      end
      
      Rails.logger.info "Found #{products.length} active products in collection #{collection_id}"
      products
    rescue => e
      Rails.logger.error "Failed to fetch collection products for #{collection_id}: #{e.message}"
      []
    end
  end

  # Fetch collection information from Shopify API
  def fetch_collection_info_from_shopify(collection_id)
    Rails.logger.info "Fetching collection info for #{collection_id} from Shopify API"
    
    query = <<~GRAPHQL
      query getCollectionInfo($id: ID!) {
        collection(id: "gid://shopify/Collection/#{collection_id}") {
          id
          title
          description
          handle
          productsCount
          updatedAt
        }
      }
    GRAPHQL

    variables = {
      id: "gid://shopify/Collection/#{collection_id}"
    }

    begin
      response = @client.query(query, variables)

      collection_data = response.body.dig("data", "collection")
      if collection_data
        {
          id: collection_data["id"],
          title: collection_data["title"],
          description: collection_data["description"],
          handle: collection_data["handle"],
          products_count: collection_data["productsCount"],
          updated_at: collection_data["updatedAt"]
        }
      else
        Rails.logger.warn "Collection #{collection_id} not found"
        nil
      end
    rescue => e
      Rails.logger.error "Failed to fetch collection info for #{collection_id}: #{e.message}"
      nil
    end
  end

  # Extract Shopify ID from GraphQL ID
  def extract_shopify_id(graphql_id)
    # GraphQL IDs are in format: "gid://shopify/Collection/123456"
    graphql_id.split('/').last
  end
end
