# Optimized Cart Service
#
# Main service that coordinates cart operations through specialized sub-services:
# - Item management via CartOperations::ItemService
# - Checkout processing via CartOperations::CheckoutService
#
# This service now acts as a facade, delegating to specialized services
# while maintaining backward compatibility with existing code.
#
class CartService
  # Custom exceptions for better error handling
  class CartError < StandardError; end
  class ItemError < StandardError; end
  class CheckoutError < StandardError; end
  class ValidationError < StandardError; end

  def initialize(user, buyer_ip = nil)
    @user = user
    @buyer_ip = buyer_ip
    # ItemService only used for bulk operations when needed
  end

  # =============================================================================
  # CART LIFECYCLE MANAGEMENT
  # =============================================================================

  # Finds existing active cart or creates a new one
  #
  # @return [Cart] Active cart for the user
  # @raise [CartError] When cart creation fails
  def find_or_create_cart
    @cart ||= find_active_cart || create_new_cart
  end

  def get_cart
    cart = find_or_create_cart
    format_enhanced_cart_data(cart)
  end







  def update_cart(params)
    cart = find_or_create_cart
    operations = []

    # Process items with bulk operations if provided
    if params[:items].present?
      items_result = update_multiple_cart_items(cart, params[:items])
      operations << {
        type: 'bulk_items_update',
        success: items_result[:success],
        items_processed: items_result[:total_items],
        success_count: items_result[:success_count],
        failure_count: items_result[:failure_count]
      }
    end
    # Return enhanced result with operation details
    format_enhanced_cart_data(cart.reload).merge(
      operations: operations,
      success: operations.all? { |op| op[:success] },
      operation_count: operations.length
    )
  end


  # =============================================================================
  # DISCOUNT MANAGEMENT REMOVED FOR SIMPLIFICATION
  # =============================================================================

  # =============================================================================
  # UTILITY METHODS
  # =============================================================================

  # Validates cart for checkout
  def validate_cart_for_checkout(cart)
    begin
      cart.validate_for_checkout!
    rescue => e
      raise CheckoutError, e.message
    end
  end


  private

  # =============================================================================
  # CART LIFECYCLE HELPERS
  # =============================================================================

  def find_active_cart
    @user.carts.where(status: "active").first
  end

  def create_new_cart
    cart = @user.carts.create(status: "active")
    raise CartError, "Failed to create cart: #{cart.errors.full_messages.join(', ')}" unless cart.persisted?
    cart
  end

  # =============================================================================
  # CART DATA FORMATTING
  # =============================================================================

  def format_enhanced_cart_data(cart)
    cart.to_enhanced_hash
  end

  # =============================================================================
  # BULK OPERATIONS HELPERS
  # =============================================================================

  def update_multiple_cart_items(cart, items_data)
    item_service = CartOperations::ItemService.new(@user)
    success = item_service.update_multiple_items(cart, items_data)

    {
      success: success,
      total_items: items_data.length,
      success_count: success ? items_data.length : 0,
      failure_count: success ? 0 : items_data.length
    }
  rescue StandardError => e
    Rails.logger.error "Bulk items update failed: #{e.message}"
    {
      success: false,
      total_items: items_data.length,
      success_count: 0,
      failure_count: items_data.length,
      error: e.message
    }
  end

  # =============================================================================
  # VALIDATION HELPERS
  # =============================================================================


end
