
class ShopifyService
  include ErrorLogging
  include ShopifyConfiguration

  class ConfigurationError < StandardError; end
  class CustomerError < StandardError; end
  class AuthenticationError < StandardError; end

  def initialize(buyer_ip = nil)
    @buyer_ip = buyer_ip
    @config = load_shopify_configuration
    @customer_service = Shopify::CustomerService.new(buyer_ip)
    @graphql_service = GraphQL::GraphQLService.new(buyer_ip)
  end

  # =============================================================================
  # PUBLIC API METHODS - Delegated to GraphQL Services
  # =============================================================================

  # Creates a new Shopify customer or returns existing one
  def create_customer(user_data)
    # Handle the expected hash interface from existing code
    email = user_data[:email]
    name = user_data[:name]
    first_name, last_name = extract_name_parts(name)

    # Check for existing customer first to avoid duplicate creation
    existing_customer = find_customer_by_email(email)
    return existing_customer if existing_customer

    # Create new customer using GraphQL service
    @customer_service.create_customer(email, first_name, last_name)
  rescue StandardError => e
    raise CustomerError, "Failed to create customer: #{e.message}"
  end

  # Finds existing customer by email address
  def find_customer_by_email(email)
    @customer_service.find_customer_by_email(email)
  rescue StandardError => e
    raise CustomerError, "Failed to find customer: #{e.message}"
  end

  # Deactivates a customer account
  def deactivate_customer(customer_id)
    @customer_service.deactivate_customer(customer_id)
  rescue StandardError => e
    raise CustomerError, "Failed to deactivate customer: #{e.message}"
  end

  # =============================================================================
  # UTILITY METHODS
  # =============================================================================

  # Check if storefront API is available
  def storefront_available?
    @config[:storefront_access_token].present?
  end

  # Get current buyer IP for API requests
  def buyer_ip
    @buyer_ip
  end

  private

  # =============================================================================
  # PRIVATE HELPER METHODS
  # =============================================================================

  # Extract first and last name from full name string

  def extract_name_parts(full_name)
    return [nil, nil] if full_name.blank?

    name_parts = full_name.split
    first_name = name_parts.first
    last_name = name_parts.length > 1 ? name_parts[1..-1].join(" ") : nil

    [first_name, last_name]
  end
end
