module GraphQL

  class GraphQLService
    include ErrorLogging
    include ShopifyConfiguration

    def initialize(buyer_ip = nil)
      @buyer_ip = buyer_ip
      @config = load_shopify_configuration
      setup_api_client
    end

    # Make Admin API GraphQL request
    def make_admin_request(query, variables = {})
      admin_url = "https://#{@config[:shop_name]}.myshopify.com/admin/api/2025-04/graphql.json"
      make_graphql_request(admin_url, query, variables, @admin_headers)
    end

    # Make Storefront API GraphQL request
    def make_storefront_request(query, variables = {})
      storefront_url = "https://#{@config[:shop_name]}.myshopify.com/api/2025-04/graphql.json"
      
      # Add buyer IP header for server-side requests (required for authenticated checkouts)
      headers = @storefront_headers.dup
      headers["Shopify-Storefront-Buyer-IP"] = @buyer_ip if @buyer_ip
      
      make_graphql_request(storefront_url, query, variables, headers)
    end

    # Check if Storefront API is available
    def storefront_available?
      @config[:storefront_access_token].present?
    end

    private

    def setup_api_client
      @admin_headers = {
        "X-Shopify-Access-Token" => @config[:admin_access_token],
        "Content-Type" => "application/json"
      }

      @storefront_headers = {
        "X-Shopify-Storefront-Access-Token" => @config[:storefront_access_token],
        "Content-Type" => "application/json"
      }
    end

    def make_graphql_request(url, query, variables, headers)
      Rails.logger.info "Making GraphQL request to: #{url}"
      Rails.logger.debug "Query: #{query.strip}"
      Rails.logger.debug "Variables: #{variables.inspect}"

      body = {
        query: query,
        variables: variables
      }.to_json

      response = RestClient::Request.execute(
        method: :post,
        url: url,
        payload: body,
        headers: headers,
        timeout: AppSetting.api_timeout
      )

      Rails.logger.info "GraphQL Response Status: #{response.code}"
      Rails.logger.debug "GraphQL Response Body: #{response.body}"

      parse_response(response)
    rescue RestClient::ExceptionWithResponse => e
      error_message = "GraphQL request failed with status #{e.response.code}: #{e.response.body}"
      Rails.logger.error error_message
      raise error_message
    end

    def parse_response(response)
      parsed = JSON.parse(response.body)
      
      # Check for GraphQL errors
      if parsed['errors']
        error_messages = parsed['errors'].map { |error| error['message'] }.join(', ')
        Rails.logger.error "GraphQL errors: #{error_messages}"
        raise "GraphQL errors: #{error_messages}"
      end

      parsed
    rescue JSON::ParserError => e
      Rails.logger.error "Invalid GraphQL response: #{e.message}"
      raise "Invalid GraphQL response: #{e.message}"
    rescue StandardError => e
      Rails.logger.error "GraphQL request failed: #{e.message}"
      Rails.logger.error "Backtrace: #{e.backtrace.first(3).join("\n")}"
      raise "GraphQL request failed: #{e.message}"
    end
  end
end
