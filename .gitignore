# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

/node_modules
/yarn-error.log

.byebug_history
.DS_Store

scripts

config/secrets*.yml
dump.rdb
data

deploy.*.sh

doc/

dump

.sass-cache

log
.rvmrc

exports
cdn/*

config/travelgator-firebase-adminsdk-fbsvc-7952955d76.json
.env
.env.dev
deploy.sh